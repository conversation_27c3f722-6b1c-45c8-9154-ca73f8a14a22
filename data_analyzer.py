#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 分析accumulator CSV数据并生成报告

功能：
1. 加载CSV数据并进行统计分析
2. 生成趋势、模式和异常分析
3. 创建可视化图表
4. 输出详细的分析报告
5. 处理缺失和无效数据

作者：AI Assistant
日期：2025-06-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
import logging
from typing import Dict, List, Tuple, Optional
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')


class DataAnalyzer:
    """数据分析器类"""
    
    def __init__(self, csv_file: str = "accumulator_data.csv", output_dir: str = "analysis_output"):
        """
        初始化数据分析器
        
        Args:
            csv_file: 输入CSV文件路径
            output_dir: 输出目录
        """
        self.csv_file = csv_file
        self.output_dir = output_dir
        self.df = None
        self.setup_logging()
        self.setup_output_dir()
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('data_analyzer.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_output_dir(self):
        """创建输出目录"""
        import os
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
    def load_data(self) -> pd.DataFrame:
        """加载CSV数据"""
        self.logger.info(f"加载数据文件: {self.csv_file}")
        
        try:
            self.df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
            self.logger.info(f"数据加载成功: {len(self.df)}行, {len(self.df.columns)}列")
            
            # 数据预处理
            self.preprocess_data()
            return self.df
            
        except FileNotFoundError:
            self.logger.error(f"文件未找到: {self.csv_file}")
            raise
        except Exception as e:
            self.logger.error(f"加载数据时出错: {str(e)}")
            raise
            
    def preprocess_data(self):
        """数据预处理"""
        self.logger.info("开始数据预处理")
        
        # 转换时间戳
        if 'timestamp' in self.df.columns:
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'], errors='coerce')
            
        # 处理数值列
        numeric_columns = [
            'total_elements', 'segment_elements', 'window_elements',
            'total_memory', 'pool_memory', 'overhead_memory', 'avg_per_element',
            'step_len', 'segment_len', 'load_factor', 'avg_chain_len'
        ]
        
        for col in numeric_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
                
        # 计算衍生指标
        self.calculate_derived_metrics()
        
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        # 内存效率 = 实际使用内存 / 总内存
        if 'actual_used' in self.df.columns and 'total_memory' in self.df.columns:
            self.df['memory_efficiency'] = self.df['actual_used'] / self.df['total_memory']
            
        # 负载因子效率
        if 'load_factor' in self.df.columns:
            self.df['load_factor_efficiency'] = np.where(
                self.df['load_factor'] > 0.75, 'High',
                np.where(self.df['load_factor'] > 0.5, 'Medium', 'Low')
            )
            
        # 元素密度 = 总元素数 / 总内存
        if 'total_elements' in self.df.columns and 'total_memory' in self.df.columns:
            self.df['element_density'] = self.df['total_elements'] / (self.df['total_memory'] / 1024)  # 每KB元素数
            
    def basic_statistics(self) -> Dict:
        """基础统计分析"""
        self.logger.info("进行基础统计分析")
        
        stats = {
            'data_overview': {
                'total_records': len(self.df),
                'feature_types': self.df['feature_type'].value_counts().to_dict(),
                'managers': self.df['manager'].value_counts().to_dict(),
                'memory_available_count': self.df['memory_available'].value_counts().to_dict()
            },
            'memory_statistics': {},
            'element_statistics': {},
            'performance_statistics': {}
        }
        
        # 内存统计
        if 'total_memory' in self.df.columns:
            memory_data = self.df['total_memory'].dropna()
            stats['memory_statistics'] = {
                'total_memory_mean': float(memory_data.mean()),
                'total_memory_median': float(memory_data.median()),
                'total_memory_std': float(memory_data.std()),
                'total_memory_min': float(memory_data.min()),
                'total_memory_max': float(memory_data.max()),
                'total_memory_sum': float(memory_data.sum())
            }
            
        # 元素统计
        if 'total_elements' in self.df.columns:
            element_data = self.df['total_elements'].dropna()
            stats['element_statistics'] = {
                'total_elements_mean': float(element_data.mean()),
                'total_elements_median': float(element_data.median()),
                'total_elements_std': float(element_data.std()),
                'total_elements_min': float(element_data.min()),
                'total_elements_max': float(element_data.max()),
                'total_elements_sum': float(element_data.sum())
            }
            
        # 性能统计
        if 'avg_per_element' in self.df.columns:
            perf_data = self.df['avg_per_element'].dropna()
            stats['performance_statistics'] = {
                'avg_per_element_mean': float(perf_data.mean()),
                'avg_per_element_median': float(perf_data.median()),
                'avg_per_element_std': float(perf_data.std()),
                'avg_per_element_min': float(perf_data.min()),
                'avg_per_element_max': float(perf_data.max())
            }
            
        return stats
        
    def detect_anomalies(self) -> Dict:
        """异常检测"""
        self.logger.info("进行异常检测")
        
        anomalies = {
            'memory_anomalies': [],
            'performance_anomalies': [],
            'load_factor_anomalies': []
        }
        
        # 内存异常检测（使用IQR方法）
        if 'total_memory' in self.df.columns:
            memory_data = self.df['total_memory'].dropna()
            Q1 = memory_data.quantile(0.25)
            Q3 = memory_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            memory_outliers = self.df[
                (self.df['total_memory'] < lower_bound) | 
                (self.df['total_memory'] > upper_bound)
            ]
            
            for _, row in memory_outliers.iterrows():
                anomalies['memory_anomalies'].append({
                    'feature_id': row['feature_id'],
                    'feature_type': row['feature_type'],
                    'total_memory': row['total_memory'],
                    'reason': 'Memory usage outlier'
                })
                
        # 性能异常检测
        if 'avg_per_element' in self.df.columns:
            perf_data = self.df['avg_per_element'].dropna()
            perf_threshold = perf_data.quantile(0.95)  # 95分位数作为阈值
            
            perf_outliers = self.df[self.df['avg_per_element'] > perf_threshold]
            
            for _, row in perf_outliers.iterrows():
                anomalies['performance_anomalies'].append({
                    'feature_id': row['feature_id'],
                    'feature_type': row['feature_type'],
                    'avg_per_element': row['avg_per_element'],
                    'reason': 'High memory per element'
                })
                
        # 负载因子异常
        if 'load_factor' in self.df.columns:
            load_factor_outliers = self.df[
                (self.df['load_factor'] > 2.0) | (self.df['load_factor'] < 0.1)
            ]
            
            for _, row in load_factor_outliers.iterrows():
                anomalies['load_factor_anomalies'].append({
                    'feature_id': row['feature_id'],
                    'feature_type': row['feature_type'],
                    'load_factor': row['load_factor'],
                    'reason': 'Unusual load factor'
                })
                
        return anomalies
        
    def create_visualizations(self):
        """创建可视化图表"""
        self.logger.info("创建可视化图表")
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        
        # 1. 特征类型分布饼图
        plt.figure(figsize=(10, 6))
        feature_counts = self.df['feature_type'].value_counts()
        plt.pie(feature_counts.values, labels=feature_counts.index, autopct='%1.1f%%')
        plt.title('特征类型分布')
        plt.savefig(f'{self.output_dir}/feature_type_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. 内存使用分布直方图
        if 'total_memory' in self.df.columns:
            plt.figure(figsize=(12, 6))
            memory_data = self.df['total_memory'].dropna()
            plt.hist(memory_data / 1024 / 1024, bins=50, alpha=0.7, edgecolor='black')
            plt.xlabel('总内存 (MB)')
            plt.ylabel('频次')
            plt.title('内存使用分布')
            plt.grid(True, alpha=0.3)
            plt.savefig(f'{self.output_dir}/memory_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        # 3. 特征类型vs平均内存使用
        if 'avg_per_element' in self.df.columns:
            plt.figure(figsize=(12, 8))
            valid_data = self.df[self.df['avg_per_element'].notna()]
            sns.boxplot(data=valid_data, x='feature_type', y='avg_per_element')
            plt.yscale('log')
            plt.xlabel('特征类型')
            plt.ylabel('平均每元素内存 (字节)')
            plt.title('不同特征类型的内存效率')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)
            plt.savefig(f'{self.output_dir}/memory_efficiency_by_type.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        # 4. 负载因子分布
        if 'load_factor' in self.df.columns:
            plt.figure(figsize=(10, 6))
            load_factor_data = self.df['load_factor'].dropna()
            plt.hist(load_factor_data, bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('负载因子')
            plt.ylabel('频次')
            plt.title('负载因子分布')
            plt.axvline(x=0.75, color='red', linestyle='--', label='理想阈值 (0.75)')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig(f'{self.output_dir}/load_factor_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            
    def generate_report(self, stats: Dict, anomalies: Dict):
        """生成分析报告"""
        self.logger.info("生成分析报告")
        
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'data_source': self.csv_file,
            'summary': {
                'total_features': stats['data_overview']['total_records'],
                'feature_types': len(stats['data_overview']['feature_types']),
                'managers': len(stats['data_overview']['managers']),
                'total_memory_gb': stats['memory_statistics'].get('total_memory_sum', 0) / (1024**3),
                'total_elements': stats['element_statistics'].get('total_elements_sum', 0)
            },
            'detailed_statistics': stats,
            'anomalies': anomalies,
            'recommendations': self.generate_recommendations(stats, anomalies)
        }
        
        # 保存JSON报告
        with open(f'{self.output_dir}/analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 生成文本报告
        self.generate_text_report(report)
        
    def generate_recommendations(self, stats: Dict, anomalies: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 内存优化建议
        if stats['memory_statistics']:
            avg_memory = stats['memory_statistics'].get('total_memory_mean', 0)
            if avg_memory > 50 * 1024 * 1024:  # 50MB
                recommendations.append("检测到高内存使用，建议优化内存分配策略")
                
        # 性能优化建议
        if len(anomalies['performance_anomalies']) > 0:
            recommendations.append(f"发现{len(anomalies['performance_anomalies'])}个性能异常特征，建议检查内存使用效率")
            
        # 负载因子建议
        if len(anomalies['load_factor_anomalies']) > 0:
            recommendations.append(f"发现{len(anomalies['load_factor_anomalies'])}个负载因子异常，建议调整哈希表大小")
            
        if not recommendations:
            recommendations.append("系统运行状态良好，未发现明显异常")
            
        return recommendations
        
    def generate_text_report(self, report: Dict):
        """生成文本格式报告"""
        with open(f'{self.output_dir}/analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write("=== 累加器数据分析报告 ===\n\n")
            f.write(f"分析时间: {report['analysis_timestamp']}\n")
            f.write(f"数据源: {report['data_source']}\n\n")
            
            f.write("=== 概要信息 ===\n")
            summary = report['summary']
            f.write(f"总特征数: {summary['total_features']}\n")
            f.write(f"特征类型数: {summary['feature_types']}\n")
            f.write(f"管理器数: {summary['managers']}\n")
            f.write(f"总内存使用: {summary['total_memory_gb']:.2f} GB\n")
            f.write(f"总元素数: {summary['total_elements']:,}\n\n")
            
            f.write("=== 异常检测结果 ===\n")
            anomalies = report['anomalies']
            f.write(f"内存异常: {len(anomalies['memory_anomalies'])}个\n")
            f.write(f"性能异常: {len(anomalies['performance_anomalies'])}个\n")
            f.write(f"负载因子异常: {len(anomalies['load_factor_anomalies'])}个\n\n")
            
            f.write("=== 优化建议 ===\n")
            for i, rec in enumerate(report['recommendations'], 1):
                f.write(f"{i}. {rec}\n")
                
    def run_analysis(self):
        """运行完整分析流程"""
        try:
            # 加载数据
            self.load_data()
            
            # 基础统计
            stats = self.basic_statistics()
            
            # 异常检测
            anomalies = self.detect_anomalies()
            
            # 创建可视化
            self.create_visualizations()
            
            # 生成报告
            self.generate_report(stats, anomalies)
            
            print(f"\n分析完成！结果已保存到: {self.output_dir}/")
            print("生成的文件:")
            print("- analysis_report.json (详细JSON报告)")
            print("- analysis_report.txt (文本摘要)")
            print("- *.png (可视化图表)")
            
        except Exception as e:
            self.logger.error(f"分析失败: {str(e)}")
            raise


def main():
    """主函数"""
    print("=== 累加器数据分析工具 ===")
    print("功能: 分析CSV数据并生成报告和可视化")
    
    # 创建分析器并运行
    analyzer = DataAnalyzer()
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
