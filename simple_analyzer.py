#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版累加器数据分析脚本

功能：
1. 生成按Feature类型聚合的Excel文件
2. 只分析内存占用不是N/A的Feature
3. 修复图表中文显示问题

作者：AI Assistant
日期：2025-06-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

# 设置matplotlib支持中文 - 修复中文显示问题
def setup_chinese_font():
    """设置中文字体"""
    try:
        # 在macOS上查找中文字体
        chinese_fonts = [
            '/System/Library/Fonts/Arial Unicode MS.ttf',
            '/System/Library/Fonts/PingFang.ttc',
            '/Library/Fonts/Arial Unicode MS.ttf',
            '/System/Library/Fonts/Helvetica.ttc'
        ]
        
        for font_path in chinese_fonts:
            if Path(font_path).exists():
                font_prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"✅ 使用字体: {font_prop.get_name()}")
                return True
    except Exception as e:
        print(f"字体设置失败: {e}")
    
    # 备用方案：使用系统默认字体
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️  使用默认字体，图表可能无法显示中文")
    return False


def load_and_filter_data():
    """加载数据并过滤有效内存记录"""
    csv_file = "accumulator_data.csv"
    if not Path(csv_file).exists():
        print(f"❌ 数据文件 {csv_file} 不存在")
        print("请先运行 log_processor.py 生成数据文件")
        return None
    
    print(f"📂 加载数据文件: {csv_file}")
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    print(f"原始数据: {len(df)} 行")
    
    # 只保留内存占用不是N/A的Feature
    valid_df = df[df['memory_available'] == True].copy()
    print(f"有效内存数据: {len(valid_df)} 行")
    
    if len(valid_df) == 0:
        print("❌ 没有有效的内存数据")
        return None
    
    return valid_df


def create_excel_report(df):
    """生成按Feature类型聚合的Excel文件"""
    print("\n📊 生成Excel报告...")
    
    # 创建Excel写入器
    excel_file = "feature_analysis_report.xlsx"
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        
        # 1. 总览表
        summary_data = []
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type]
            summary_data.append({
                'Feature类型': feature_type,
                'Feature数量': len(type_data),
                '总内存(MB)': type_data['total_memory'].sum() / 1024 / 1024,
                '平均内存(MB)': type_data['total_memory'].mean() / 1024 / 1024,
                '总元素数': type_data['total_elements'].sum(),
                '平均元素数': type_data['total_elements'].mean(),
                '平均每元素内存(字节)': (type_data['total_memory'].sum() / type_data['total_elements'].sum()) if type_data['total_elements'].sum() > 0 else 0
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='总览', index=False)
        
        # 2. 按Feature类型分别创建工作表
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type].copy()
            
            # 按FeatureID排序
            type_data = type_data.sort_values('feature_id')
            
            # 选择关键列
            columns_to_export = [
                'feature_id', 'total_elements', 'total_memory', 
                'avg_per_element', 'load_factor', 'timestamp'
            ]
            
            # 确保列存在
            export_columns = [col for col in columns_to_export if col in type_data.columns]
            export_data = type_data[export_columns].copy()
            
            # 格式化数据
            if 'total_memory' in export_data.columns:
                export_data['total_memory_MB'] = export_data['total_memory'] / 1024 / 1024
            
            # 写入工作表
            sheet_name = f"{feature_type}_详情"
            export_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            print(f"  ✅ {feature_type}: {len(type_data)} 个Feature")
    
    print(f"✅ Excel报告已生成: {excel_file}")
    return excel_file


def create_simple_charts(df):
    """生成简单的图表"""
    print("\n📈 生成图表...")
    
    # 创建输出目录
    output_dir = Path("charts")
    output_dir.mkdir(exist_ok=True)
    
    # 1. Feature类型分布饼图
    plt.figure(figsize=(10, 8))
    feature_counts = df['feature_type'].value_counts()
    
    # 使用英文标签避免中文显示问题
    labels = [f"{ftype}\n({count} features)" for ftype, count in feature_counts.items()]
    
    plt.pie(feature_counts.values, labels=labels, autopct='%1.1f%%', startangle=90)
    plt.title('Feature Type Distribution', fontsize=16, pad=20)
    plt.axis('equal')
    plt.tight_layout()
    plt.savefig(output_dir / 'feature_type_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 内存使用对比柱状图
    plt.figure(figsize=(12, 8))
    memory_by_type = df.groupby('feature_type')['total_memory'].sum() / 1024 / 1024 / 1024  # GB
    
    bars = plt.bar(memory_by_type.index, memory_by_type.values)
    plt.title('Memory Usage by Feature Type', fontsize=16, pad=20)
    plt.xlabel('Feature Type', fontsize=12)
    plt.ylabel('Total Memory (GB)', fontsize=12)
    
    # 在柱状图上添加数值标签
    for bar, value in zip(bars, memory_by_type.values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{value:.2f}GB', ha='center', va='bottom', fontsize=10)
    
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_dir / 'memory_usage_by_type.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 内存效率散点图
    plt.figure(figsize=(12, 8))
    
    # 计算内存效率
    df_plot = df.copy()
    df_plot['memory_per_element'] = df_plot['total_memory'] / df_plot['total_elements']
    
    colors = {'SEGMENT': 'red', 'RATIO': 'blue', 'DISTRIBUTION': 'green'}
    
    for feature_type in df_plot['feature_type'].unique():
        type_data = df_plot[df_plot['feature_type'] == feature_type]
        plt.scatter(type_data['total_elements'], type_data['memory_per_element'], 
                   label=feature_type, alpha=0.6, s=50, 
                   color=colors.get(feature_type, 'gray'))
    
    plt.xlabel('Total Elements', fontsize=12)
    plt.ylabel('Memory per Element (bytes)', fontsize=12)
    plt.title('Memory Efficiency Analysis', fontsize=16, pad=20)
    plt.legend()
    plt.xscale('log')
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_dir / 'memory_efficiency.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 图表已保存到 {output_dir}/ 目录")
    return output_dir


def generate_summary_report(df, excel_file, chart_dir):
    """生成简要分析报告"""
    print("\n📝 生成分析报告...")
    
    report_file = "analysis_summary.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 累加器Feature分析报告 ===\n\n")
        f.write(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据源: accumulator_data.csv\n\n")
        
        # 基础统计
        f.write("=== 数据概览 ===\n")
        f.write(f"有效Feature数量: {len(df)}\n")
        f.write(f"Feature类型: {', '.join(sorted(df['feature_type'].unique()))}\n\n")
        
        # 按类型统计
        f.write("=== 按Feature类型统计 ===\n")
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type]
            total_memory_gb = type_data['total_memory'].sum() / 1024**3
            total_elements = type_data['total_elements'].sum()
            avg_memory_per_element = type_data['total_memory'].sum() / total_elements if total_elements > 0 else 0
            
            f.write(f"\n{feature_type}:\n")
            f.write(f"  - Feature数量: {len(type_data)}\n")
            f.write(f"  - 总内存: {total_memory_gb:.2f} GB\n")
            f.write(f"  - 总元素数: {total_elements:,}\n")
            f.write(f"  - 平均每元素内存: {avg_memory_per_element:.2f} 字节\n")
        
        # 内存使用Top 10
        f.write(f"\n=== 内存使用Top 10 ===\n")
        top_memory = df.nlargest(10, 'total_memory')[['feature_id', 'feature_type', 'total_memory', 'total_elements']]
        for _, row in top_memory.iterrows():
            memory_mb = row['total_memory'] / 1024 / 1024
            f.write(f"Feature {row['feature_id']} ({row['feature_type']}): {memory_mb:.2f} MB, {row['total_elements']:,} 元素\n")
        
        f.write(f"\n=== 生成文件 ===\n")
        f.write(f"Excel报告: {excel_file}\n")
        f.write(f"图表目录: {chart_dir}/\n")
        f.write(f"分析报告: {report_file}\n")
    
    print(f"✅ 分析报告已保存: {report_file}")


def main():
    """主函数"""
    print("🚀 简化版累加器数据分析工具")
    print("=" * 50)
    
    # 设置中文字体
    setup_chinese_font()
    
    # 加载和过滤数据
    df = load_and_filter_data()
    if df is None:
        return
    
    # 生成Excel报告
    excel_file = create_excel_report(df)
    
    # 生成图表
    chart_dir = create_simple_charts(df)
    
    # 生成总结报告
    generate_summary_report(df, excel_file, chart_dir)
    
    print("\n🎉 分析完成！")
    print("生成的文件:")
    print(f"📊 Excel报告: {excel_file}")
    print(f"📈 图表目录: {chart_dir}/")
    print(f"📝 分析报告: analysis_summary.txt")


if __name__ == "__main__":
    main()
