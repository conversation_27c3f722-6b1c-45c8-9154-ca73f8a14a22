#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版累加器数据分析脚本

功能：
1. 生成按Feature类型聚合的Excel文件
2. 只分析内存占用不是N/A的Feature
3. 修复图表中文显示问题

作者：AI Assistant
日期：2025-06-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

# 设置matplotlib支持中文 - 修复中文显示问题
def setup_chinese_font():
    """设置中文字体"""
    try:
        # 在macOS上查找中文字体
        chinese_fonts = [
            '/System/Library/Fonts/Arial Unicode MS.ttf',
            '/System/Library/Fonts/PingFang.ttc',
            '/Library/Fonts/Arial Unicode MS.ttf',
            '/System/Library/Fonts/Helvetica.ttc'
        ]
        
        for font_path in chinese_fonts:
            if Path(font_path).exists():
                font_prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"✅ 使用字体: {font_prop.get_name()}")
                return True
    except Exception as e:
        print(f"字体设置失败: {e}")
    
    # 备用方案：使用系统默认字体
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️  使用默认字体，图表可能无法显示中文")
    return False


def load_and_filter_data():
    """加载数据并过滤有效内存记录"""
    csv_file = "accumulator_data.csv"
    if not Path(csv_file).exists():
        print(f"❌ 数据文件 {csv_file} 不存在")
        print("请先运行 log_processor.py 生成数据文件")
        return None
    
    print(f"📂 加载数据文件: {csv_file}")
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    print(f"原始数据: {len(df)} 行")
    
    # 只保留内存占用不是N/A的Feature
    valid_df = df[df['memory_available'] == True].copy()
    print(f"有效内存数据: {len(valid_df)} 行")
    
    if len(valid_df) == 0:
        print("❌ 没有有效的内存数据")
        return None
    
    return valid_df


def create_excel_report(df):
    """生成加强版Excel文件，包含详细的Feature数据"""
    print("\n📊 生成加强版Excel报告...")

    # 创建Excel写入器
    excel_file = "feature_detailed_analysis.xlsx"

    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:

        # 1. 总览表
        summary_data = []
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type]
            summary_data.append({
                'Feature类型': feature_type,
                'Feature数量': len(type_data),
                '总内存(MB)': round(type_data['total_memory'].sum() / 1024 / 1024, 2),
                '平均内存(MB)': round(type_data['total_memory'].mean() / 1024 / 1024, 2),
                '最大内存(MB)': round(type_data['total_memory'].max() / 1024 / 1024, 2),
                '最小内存(MB)': round(type_data['total_memory'].min() / 1024 / 1024, 2),
                '总元素数': int(type_data['total_elements'].sum()),
                '平均元素数': int(type_data['total_elements'].mean()),
                '最大元素数': int(type_data['total_elements'].max()),
                '平均每元素内存(字节)': round((type_data['total_memory'].sum() / type_data['total_elements'].sum()) if type_data['total_elements'].sum() > 0 else 0, 2)
            })

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='总览', index=False)

        # 2. 全部数据表 - 按Feature类型聚合，类型内按FeatureID排序
        all_data = df.copy()
        all_data = all_data.sort_values(['feature_type', 'feature_id'])

        # 选择所有有用的列进行导出
        columns_to_export = [
            'feature_type', 'feature_id', 'manager', 'timestamp',
            'total_elements', 'segment_elements', 'window_elements',
            'total_memory', 'pool_memory', 'overhead_memory', 'avg_per_element',
            'step_len', 'step_num', 'max_step_num', 'segment_len',
            'oldest_coord', 'segment_start_coord', 'latest_coord',
            'segment_size', 'bucket_count', 'load_factor',
            'hashmap_size', 'hashmap_buckets', 'hashmap_load_factor',
            'empty_buckets', 'non_empty_buckets', 'max_chain_len', 'avg_chain_len',
            'window_step_count', 'node_num', 'malloc_num', 'used_blocks',
            'mem_consume', 'actual_need', 'actual_used'
        ]

        # 确保列存在并重命名为中文
        column_mapping = {
            'feature_type': 'Feature类型',
            'feature_id': 'FeatureID',
            'manager': '管理器',
            'timestamp': '时间戳',
            'total_elements': '总元素数',
            'segment_elements': '段元素数',
            'window_elements': '窗口元素数',
            'total_memory': '总内存(字节)',
            'pool_memory': '内存池(字节)',
            'overhead_memory': '开销内存(字节)',
            'avg_per_element': '平均每元素内存(字节)',
            'step_len': '步长',
            'step_num': '步数',
            'max_step_num': '最大步数',
            'segment_len': '段长度',
            'oldest_coord': '最旧坐标',
            'segment_start_coord': '段起始坐标',
            'latest_coord': '最新坐标',
            'segment_size': '段大小',
            'bucket_count': '桶数量',
            'load_factor': '负载因子',
            'hashmap_size': '哈希表大小',
            'hashmap_buckets': '哈希表桶数',
            'hashmap_load_factor': '哈希表负载因子',
            'empty_buckets': '空桶数',
            'non_empty_buckets': '非空桶数',
            'max_chain_len': '最大链长',
            'avg_chain_len': '平均链长',
            'window_step_count': '窗口步数',
            'node_num': '节点数',
            'malloc_num': '分配数',
            'used_blocks': '使用块数',
            'mem_consume': '内存消耗',
            'actual_need': '实际需求',
            'actual_used': '实际使用'
        }

        export_columns = [col for col in columns_to_export if col in all_data.columns]
        export_data = all_data[export_columns].copy()

        # 添加计算列
        export_data['内存(MB)'] = export_data['total_memory'] / 1024 / 1024
        export_data['内存效率(字节/元素)'] = export_data['total_memory'] / export_data['total_elements']

        # 重命名列
        export_data = export_data.rename(columns=column_mapping)

        # 写入全部数据表
        export_data.to_excel(writer, sheet_name='全部数据', index=False)

        # 3. 按Feature类型分别创建详细工作表
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type].copy()
            type_data = type_data.sort_values('feature_id')

            # 使用相同的列和重命名
            type_export_columns = [col for col in export_columns if col in type_data.columns]
            type_export_data = type_data[type_export_columns].copy()

            # 添加计算列
            type_export_data['内存(MB)'] = type_export_data['total_memory'] / 1024 / 1024
            type_export_data['内存效率(字节/元素)'] = type_export_data['total_memory'] / type_export_data['total_elements']

            # 重命名列
            type_export_data = type_export_data.rename(columns=column_mapping)

            # 写入工作表
            sheet_name = f"{feature_type}_详情"
            type_export_data.to_excel(writer, sheet_name=sheet_name, index=False)

            print(f"  ✅ {feature_type}: {len(type_data)} 个Feature，{len(type_export_data.columns)} 列数据")

    print(f"✅ 加强版Excel报告已生成: {excel_file}")
    print(f"包含工作表: 总览、全部数据、各Feature类型详情")
    return excel_file


def create_simple_charts(df):
    """生成简单的图表"""
    print("\n📈 生成图表...")
    
    # 创建输出目录
    output_dir = Path("charts")
    output_dir.mkdir(exist_ok=True)
    
    # 1. Feature类型分布饼图
    plt.figure(figsize=(10, 8))
    feature_counts = df['feature_type'].value_counts()
    
    # 使用英文标签避免中文显示问题
    labels = [f"{ftype}\n({count} features)" for ftype, count in feature_counts.items()]
    
    plt.pie(feature_counts.values, labels=labels, autopct='%1.1f%%', startangle=90)
    plt.title('Feature Type Distribution', fontsize=16, pad=20)
    plt.axis('equal')
    plt.tight_layout()
    plt.savefig(output_dir / 'feature_type_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 内存使用对比柱状图
    plt.figure(figsize=(12, 8))
    memory_by_type = df.groupby('feature_type')['total_memory'].sum() / 1024 / 1024 / 1024  # GB
    
    bars = plt.bar(memory_by_type.index, memory_by_type.values)
    plt.title('Memory Usage by Feature Type', fontsize=16, pad=20)
    plt.xlabel('Feature Type', fontsize=12)
    plt.ylabel('Total Memory (GB)', fontsize=12)
    
    # 在柱状图上添加数值标签
    for bar, value in zip(bars, memory_by_type.values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{value:.2f}GB', ha='center', va='bottom', fontsize=10)
    
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_dir / 'memory_usage_by_type.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 内存效率散点图
    plt.figure(figsize=(12, 8))
    
    # 计算内存效率
    df_plot = df.copy()
    df_plot['memory_per_element'] = df_plot['total_memory'] / df_plot['total_elements']
    
    colors = {'SEGMENT': 'red', 'RATIO': 'blue', 'DISTRIBUTION': 'green'}
    
    for feature_type in df_plot['feature_type'].unique():
        type_data = df_plot[df_plot['feature_type'] == feature_type]
        plt.scatter(type_data['total_elements'], type_data['memory_per_element'], 
                   label=feature_type, alpha=0.6, s=50, 
                   color=colors.get(feature_type, 'gray'))
    
    plt.xlabel('Total Elements', fontsize=12)
    plt.ylabel('Memory per Element (bytes)', fontsize=12)
    plt.title('Memory Efficiency Analysis', fontsize=16, pad=20)
    plt.legend()
    plt.xscale('log')
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(output_dir / 'memory_efficiency.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 图表已保存到 {output_dir}/ 目录")
    return output_dir


def generate_summary_report(df, excel_file, chart_dir):
    """生成简要分析报告"""
    print("\n📝 生成分析报告...")
    
    report_file = "analysis_summary.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 累加器Feature分析报告 ===\n\n")
        f.write(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据源: accumulator_data.csv\n\n")
        
        # 基础统计
        f.write("=== 数据概览 ===\n")
        f.write(f"有效Feature数量: {len(df)}\n")
        f.write(f"Feature类型: {', '.join(sorted(df['feature_type'].unique()))}\n\n")
        
        # 按类型统计
        f.write("=== 按Feature类型统计 ===\n")
        for feature_type in sorted(df['feature_type'].unique()):
            type_data = df[df['feature_type'] == feature_type]
            total_memory_gb = type_data['total_memory'].sum() / 1024**3
            total_elements = type_data['total_elements'].sum()
            avg_memory_per_element = type_data['total_memory'].sum() / total_elements if total_elements > 0 else 0
            
            f.write(f"\n{feature_type}:\n")
            f.write(f"  - Feature数量: {len(type_data)}\n")
            f.write(f"  - 总内存: {total_memory_gb:.2f} GB\n")
            f.write(f"  - 总元素数: {total_elements:,}\n")
            f.write(f"  - 平均每元素内存: {avg_memory_per_element:.2f} 字节\n")
        
        # 内存使用Top 10
        f.write(f"\n=== 内存使用Top 10 ===\n")
        top_memory = df.nlargest(10, 'total_memory')[['feature_id', 'feature_type', 'total_memory', 'total_elements']]
        for _, row in top_memory.iterrows():
            memory_mb = row['total_memory'] / 1024 / 1024
            f.write(f"Feature {row['feature_id']} ({row['feature_type']}): {memory_mb:.2f} MB, {row['total_elements']:,} 元素\n")
        
        f.write(f"\n=== 生成文件 ===\n")
        f.write(f"Excel详细报告: {excel_file}\n")
        f.write(f"  - 总览工作表: 各Feature类型汇总统计\n")
        f.write(f"  - 全部数据工作表: 所有Feature详细数据\n")
        f.write(f"  - 分类详情工作表: 按Feature类型分别展示\n")
        f.write(f"图表目录: {chart_dir}/\n")
        f.write(f"分析报告: {report_file}\n")
    
    print(f"✅ 分析报告已保存: {report_file}")


def main():
    """主函数"""
    print("🚀 简化版累加器数据分析工具")
    print("=" * 50)
    
    # 设置中文字体
    setup_chinese_font()
    
    # 加载和过滤数据
    df = load_and_filter_data()
    if df is None:
        return
    
    # 生成Excel报告
    excel_file = create_excel_report(df)
    
    # 生成图表
    chart_dir = create_simple_charts(df)
    
    # 生成总结报告
    generate_summary_report(df, excel_file, chart_dir)
    
    print("\n🎉 分析完成！")
    print("生成的文件:")
    print(f"📊 Excel详细报告: {excel_file}")
    print(f"   包含: 总览、全部数据、各类型详情工作表")
    print(f"📈 图表目录: {chart_dir}/")
    print(f"📝 分析报告: analysis_summary.txt")


if __name__ == "__main__":
    main()
