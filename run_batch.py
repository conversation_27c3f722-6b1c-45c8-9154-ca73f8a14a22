#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版批量分析运行脚本

使用方法：
1. 将此脚本放在包含日志文件的文件夹中
2. 运行: python run_batch.py
3. 或者指定文件夹: python run_batch.py <文件夹路径>

作者：AI Assistant
日期：2025-06-19
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def find_log_files(folder_path):
    """查找文件夹中的日志文件"""
    folder = Path(folder_path)
    if not folder.exists():
        return []
    
    log_files = []
    log_extensions = ['.log', '.txt']
    
    for file_path in folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in log_extensions:
            log_files.append(file_path)
    
    return sorted(log_files)


def analyze_single_file(log_file, output_base_dir):
    """分析单个日志文件"""
    print(f"\n{'='*50}")
    print(f"🔄 分析: {log_file.name}")
    print(f"{'='*50}")
    
    # 创建输出目录
    output_dir_name = log_file.stem  # 文件名（不含扩展名）
    output_dir = Path(output_base_dir) / output_dir_name
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 输出目录: {output_dir}")
    
    # 复制日志文件
    target_log = output_dir / "accumulator.log"
    shutil.copy2(log_file, target_log)
    
    # 复制脚本
    scripts = ['log_processor.py', 'simple_analyzer.py']
    for script in scripts:
        if Path(script).exists():
            shutil.copy2(script, output_dir)
        else:
            print(f"❌ 缺少脚本: {script}")
            return False
    
    # 切换到输出目录执行分析
    original_dir = os.getcwd()
    try:
        os.chdir(output_dir)
        
        # 步骤1: 处理日志
        print("🔄 处理日志文件...")
        result1 = subprocess.run([sys.executable, "log_processor.py"], 
                               capture_output=True, text=True, encoding='utf-8')
        
        if result1.returncode != 0:
            print(f"❌ 日志处理失败: {result1.stderr}")
            return False
        
        # 提取关键信息
        if "总记录数:" in result1.stdout:
            for line in result1.stdout.split('\n'):
                if "总记录数:" in line or "内存可用性:" in line:
                    print(f"   {line.strip()}")
        
        # 步骤2: 生成分析报告
        print("🔄 生成分析报告...")
        result2 = subprocess.run([sys.executable, "simple_analyzer.py"], 
                               capture_output=True, text=True, encoding='utf-8')
        
        if result2.returncode != 0:
            print(f"❌ 分析失败: {result2.stderr}")
            return False
        
        # 提取关键信息
        if "有效内存数据:" in result2.stdout:
            for line in result2.stdout.split('\n'):
                if "有效内存数据:" in line or "✅" in line:
                    print(f"   {line.strip()}")
        
        # 清理脚本文件
        for script in scripts:
            script_path = Path(script)
            if script_path.exists():
                script_path.unlink()
        
        print(f"✅ {log_file.name} 分析完成")
        return True
        
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        return False
    finally:
        os.chdir(original_dir)


def main():
    """主函数"""
    print("🚀 批量累加器日志分析工具")
    print("=" * 40)
    
    # 确定日志文件夹
    if len(sys.argv) > 1:
        log_folder = sys.argv[1]
    else:
        log_folder = input("请输入日志文件夹路径 (回车使用当前目录): ").strip()
        if not log_folder:
            log_folder = "."
    
    log_folder = Path(log_folder)
    if not log_folder.exists():
        print(f"❌ 文件夹不存在: {log_folder}")
        return
    
    print(f"📁 日志文件夹: {log_folder.absolute()}")
    
    # 查找日志文件
    log_files = find_log_files(log_folder)
    
    if not log_files:
        print("❌ 未找到日志文件 (.log 或 .txt)")
        return
    
    print(f"\n📋 找到 {len(log_files)} 个日志文件:")
    for i, log_file in enumerate(log_files, 1):
        print(f"  {i}. {log_file.name}")
    
    # 确认继续
    response = input(f"\n是否继续分析? (y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 用户取消")
        return
    
    # 设置输出目录
    output_base = "batch_results"
    Path(output_base).mkdir(exist_ok=True)
    
    # 批量分析
    results = {}
    for i, log_file in enumerate(log_files, 1):
        print(f"\n进度: {i}/{len(log_files)}")
        success = analyze_single_file(log_file, output_base)
        results[log_file.name] = success
    
    # 生成汇总
    summary_file = Path(output_base) / "batch_summary.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("=== 批量分析汇总 ===\n\n")
        f.write(f"日志文件夹: {log_folder.absolute()}\n")
        f.write(f"输出目录: {Path(output_base).absolute()}\n\n")
        
        successful = sum(1 for success in results.values() if success)
        f.write(f"总文件数: {len(results)}\n")
        f.write(f"成功: {successful}\n")
        f.write(f"失败: {len(results) - successful}\n\n")
        
        f.write("详细结果:\n")
        for filename, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            f.write(f"{filename}: {status}\n")
    
    # 显示最终结果
    successful = sum(1 for success in results.values() if success)
    failed = len(results) - successful
    
    print(f"\n🎉 批量分析完成！")
    print(f"📊 总计: {len(log_files)} 个文件")
    print(f"✅ 成功: {successful} 个")
    print(f"❌ 失败: {failed} 个")
    print(f"📁 结果目录: {Path(output_base).absolute()}")
    print(f"📋 汇总报告: {summary_file}")
    
    # 显示目录结构
    print(f"\n📂 生成的目录结构:")
    print(f"{output_base}/")
    for filename, success in results.items():
        if success:
            dir_name = Path(filename).stem
            print(f"├── {dir_name}/")
            print(f"│   ├── accumulator.log")
            print(f"│   ├── accumulator_data.csv")
            print(f"│   ├── feature_detailed_analysis.xlsx")
            print(f"│   ├── analysis_summary.txt")
            print(f"│   ├── charts/")
            print(f"│   └── log_processor.log")


if __name__ == "__main__":
    main()
