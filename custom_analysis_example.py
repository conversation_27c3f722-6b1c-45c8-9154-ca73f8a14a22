#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义分析示例脚本

展示如何使用生成的CSV数据进行自定义分析

作者：AI Assistant
日期：2025-06-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def load_data():
    """加载数据"""
    csv_file = "accumulator_data.csv"
    if not Path(csv_file).exists():
        print(f"❌ 数据文件 {csv_file} 不存在")
        print("请先运行 log_processor.py 生成数据文件")
        return None
    
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    print(f"✅ 成功加载数据: {len(df)} 行, {len(df.columns)} 列")
    return df


def analyze_memory_efficiency(df):
    """分析内存效率"""
    print("\n=== 内存效率分析 ===")
    
    # 过滤有效数据
    valid_data = df[df['memory_available'] == True].copy()
    
    if len(valid_data) == 0:
        print("❌ 没有有效的内存数据")
        return
    
    # 计算内存效率指标
    valid_data['memory_per_element'] = valid_data['total_memory'] / valid_data['total_elements']
    valid_data['memory_utilization'] = valid_data['actual_used'] / valid_data['total_memory']
    
    # 按特征类型分组分析
    efficiency_by_type = valid_data.groupby('feature_type').agg({
        'memory_per_element': ['mean', 'median', 'std'],
        'memory_utilization': ['mean', 'median', 'std'],
        'total_memory': ['sum', 'mean'],
        'total_elements': ['sum', 'mean']
    }).round(2)
    
    print("按特征类型的内存效率统计:")
    print(efficiency_by_type)
    
    # 找出内存效率最差的特征
    worst_efficiency = valid_data.nlargest(10, 'memory_per_element')[
        ['feature_id', 'feature_type', 'total_elements', 'total_memory', 'memory_per_element']
    ]
    
    print("\n内存效率最差的10个特征:")
    print(worst_efficiency)
    
    return valid_data


def analyze_load_factor_performance(df):
    """分析负载因子与性能的关系"""
    print("\n=== 负载因子性能分析 ===")
    
    # 过滤有负载因子数据的记录
    load_factor_data = df[df['load_factor'].notna()].copy()
    
    if len(load_factor_data) == 0:
        print("❌ 没有负载因子数据")
        return
    
    # 定义负载因子区间
    load_factor_data['load_factor_range'] = pd.cut(
        load_factor_data['load_factor'], 
        bins=[0, 0.5, 0.75, 1.0, 1.5, 2.0, float('inf')],
        labels=['很低(<0.5)', '低(0.5-0.75)', '理想(0.75-1.0)', '高(1.0-1.5)', '很高(1.5-2.0)', '过高(>2.0)']
    )
    
    # 分析不同负载因子区间的性能
    performance_by_load_factor = load_factor_data.groupby('load_factor_range').agg({
        'avg_per_element': ['mean', 'median'],
        'max_chain_len': ['mean', 'median'],
        'avg_chain_len': ['mean', 'median'],
        'feature_id': 'count'
    }).round(2)
    
    print("不同负载因子区间的性能统计:")
    print(performance_by_load_factor)
    
    return load_factor_data


def find_memory_hotspots(df):
    """找出内存热点"""
    print("\n=== 内存热点分析 ===")
    
    valid_data = df[df['memory_available'] == True].copy()
    
    # 计算内存使用百分比
    total_memory = valid_data['total_memory'].sum()
    valid_data['memory_percentage'] = (valid_data['total_memory'] / total_memory * 100).round(2)
    
    # 找出占用内存最多的特征
    memory_hotspots = valid_data.nlargest(20, 'total_memory')[
        ['feature_id', 'feature_type', 'manager', 'total_memory', 'memory_percentage', 'total_elements']
    ]
    
    print("内存使用最多的20个特征:")
    print(memory_hotspots)
    
    # 按管理器统计内存使用
    memory_by_manager = valid_data.groupby('manager').agg({
        'total_memory': ['sum', 'mean', 'count'],
        'total_elements': 'sum'
    }).round(2)
    
    print("\n按管理器的内存使用统计:")
    print(memory_by_manager)
    
    return memory_hotspots


def create_custom_visualizations(df):
    """创建自定义可视化"""
    print("\n=== 生成自定义可视化图表 ===")
    
    # 创建输出目录
    output_dir = Path("custom_analysis")
    output_dir.mkdir(exist_ok=True)
    
    valid_data = df[df['memory_available'] == True].copy()
    
    # 1. 内存使用vs元素数量散点图
    plt.figure(figsize=(12, 8))
    for feature_type in valid_data['feature_type'].unique():
        type_data = valid_data[valid_data['feature_type'] == feature_type]
        plt.scatter(type_data['total_elements'], type_data['total_memory'] / 1024 / 1024, 
                   label=feature_type, alpha=0.6, s=50)
    
    plt.xlabel('总元素数')
    plt.ylabel('总内存使用 (MB)')
    plt.title('内存使用 vs 元素数量')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xscale('log')
    plt.yscale('log')
    plt.savefig(output_dir / 'memory_vs_elements.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 负载因子分布热力图
    if 'load_factor' in valid_data.columns:
        load_factor_data = valid_data[valid_data['load_factor'].notna()]
        if len(load_factor_data) > 0:
            plt.figure(figsize=(10, 6))
            
            # 创建负载因子vs特征类型的热力图数据
            heatmap_data = load_factor_data.groupby(['feature_type', 
                pd.cut(load_factor_data['load_factor'], bins=10)])['feature_id'].count().unstack(fill_value=0)
            
            sns.heatmap(heatmap_data, annot=True, fmt='d', cmap='YlOrRd')
            plt.title('负载因子分布热力图')
            plt.xlabel('负载因子区间')
            plt.ylabel('特征类型')
            plt.xticks(rotation=45)
            plt.savefig(output_dir / 'load_factor_heatmap.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    # 3. 内存效率箱线图
    if len(valid_data) > 0:
        valid_data['memory_per_element'] = valid_data['total_memory'] / valid_data['total_elements']
        
        plt.figure(figsize=(10, 6))
        sns.boxplot(data=valid_data, x='feature_type', y='memory_per_element')
        plt.yscale('log')
        plt.title('不同特征类型的内存效率分布')
        plt.xlabel('特征类型')
        plt.ylabel('平均每元素内存 (字节, 对数刻度)')
        plt.xticks(rotation=45)
        plt.savefig(output_dir / 'memory_efficiency_boxplot.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print(f"✅ 自定义图表已保存到 {output_dir}/ 目录")


def generate_custom_report(df):
    """生成自定义报告"""
    print("\n=== 生成自定义分析报告 ===")
    
    report_file = "custom_analysis_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 累加器自定义分析报告 ===\n\n")
        f.write(f"分析时间: {pd.Timestamp.now()}\n")
        f.write(f"数据源: accumulator_data.csv\n\n")
        
        # 基础统计
        f.write("=== 数据概览 ===\n")
        f.write(f"总记录数: {len(df)}\n")
        f.write(f"有效内存记录数: {len(df[df['memory_available'] == True])}\n")
        f.write(f"特征类型: {', '.join(df['feature_type'].unique())}\n")
        f.write(f"管理器: {', '.join(df['manager'].unique())}\n\n")
        
        # 内存统计
        valid_data = df[df['memory_available'] == True]
        if len(valid_data) > 0:
            f.write("=== 内存使用统计 ===\n")
            f.write(f"总内存使用: {valid_data['total_memory'].sum() / 1024**3:.2f} GB\n")
            f.write(f"平均内存使用: {valid_data['total_memory'].mean() / 1024**2:.2f} MB\n")
            f.write(f"最大内存使用: {valid_data['total_memory'].max() / 1024**2:.2f} MB\n")
            f.write(f"内存使用标准差: {valid_data['total_memory'].std() / 1024**2:.2f} MB\n\n")
        
        # 性能统计
        if 'avg_per_element' in valid_data.columns:
            perf_data = valid_data['avg_per_element'].dropna()
            if len(perf_data) > 0:
                f.write("=== 性能统计 ===\n")
                f.write(f"平均每元素内存: {perf_data.mean():.2f} 字节\n")
                f.write(f"中位数每元素内存: {perf_data.median():.2f} 字节\n")
                f.write(f"最大每元素内存: {perf_data.max():.2f} 字节\n")
                f.write(f"最小每元素内存: {perf_data.min():.2f} 字节\n\n")
        
        # 建议
        f.write("=== 优化建议 ===\n")
        
        # 内存优化建议
        if len(valid_data) > 0:
            high_memory_features = valid_data[valid_data['total_memory'] > valid_data['total_memory'].quantile(0.9)]
            if len(high_memory_features) > 0:
                f.write(f"1. 发现 {len(high_memory_features)} 个高内存使用特征，建议优化\n")
        
        # 负载因子建议
        if 'load_factor' in valid_data.columns:
            load_factor_data = valid_data['load_factor'].dropna()
            if len(load_factor_data) > 0:
                high_load_factor = len(load_factor_data[load_factor_data > 1.5])
                low_load_factor = len(load_factor_data[load_factor_data < 0.5])
                if high_load_factor > 0:
                    f.write(f"2. 发现 {high_load_factor} 个高负载因子特征，建议调整哈希表大小\n")
                if low_load_factor > 0:
                    f.write(f"3. 发现 {low_load_factor} 个低负载因子特征，可能存在内存浪费\n")
    
    print(f"✅ 自定义报告已保存为: {report_file}")


def main():
    """主函数"""
    print("🔍 累加器数据自定义分析工具")
    print("=" * 50)
    
    # 加载数据
    df = load_data()
    if df is None:
        return
    
    # 执行各种分析
    analyze_memory_efficiency(df)
    analyze_load_factor_performance(df)
    find_memory_hotspots(df)
    create_custom_visualizations(df)
    generate_custom_report(df)
    
    print("\n🎉 自定义分析完成！")
    print("生成的文件:")
    print("- custom_analysis_report.txt (自定义分析报告)")
    print("- custom_analysis/ (自定义图表目录)")


if __name__ == "__main__":
    main()
