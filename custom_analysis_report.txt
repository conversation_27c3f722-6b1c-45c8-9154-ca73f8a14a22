=== 累加器自定义分析报告 ===

分析时间: 2025-06-19 16:02:34.176153
数据源: accumulator_data.csv

=== 数据概览 ===
总记录数: 2034
有效内存记录数: 1803
特征类型: SEGMENT, RATIO, DISTRIBUTION
管理器: CLICK_CPC_LOG, CLICK_CPM_LOG

=== 内存使用统计 ===
总内存使用: 26.31 GB
平均内存使用: 14.94 MB
最大内存使用: 201.51 MB
内存使用标准差: 10.46 MB

=== 性能统计 ===
平均每元素内存: 169363.52 字节
中位数每元素内存: 661.70 字节
最大每元素内存: 6227814.00 字节
最小每元素内存: 0.00 字节

=== 优化建议 ===
1. 发现 181 个高内存使用特征，建议优化
2. 发现 574 个高负载因子特征，建议调整哈希表大小
3. 发现 95 个低负载因子特征，可能存在内存浪费
