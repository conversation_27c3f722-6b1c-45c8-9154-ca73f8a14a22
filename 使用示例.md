# 累加器日志分析工具使用示例

## 批量分析示例

### 场景
假设您有一个包含多个配置的日志文件夹：
```
logs/
├── baseline_config.log
├── optimized_config_v1.log
├── optimized_config_v2.log
├── experimental_config.log
└── production_config.log
```

### 使用方法

#### 方法1：交互式运行
```bash
python run_batch.py
```
然后输入日志文件夹路径：`logs`

#### 方法2：直接指定路径
```bash
python run_batch.py logs
```

### 生成结果
```
batch_results/
├── batch_summary.txt                    # 汇总报告
├── baseline_config/                     # 基线配置分析结果
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   │   ├── feature_type_distribution.png
│   │   ├── memory_usage_by_type.png
│   │   └── memory_efficiency.png
│   └── log_processor.log
├── optimized_config_v1/                 # 优化配置v1分析结果
│   └── ...
├── optimized_config_v2/                 # 优化配置v2分析结果
│   └── ...
├── experimental_config/                 # 实验配置分析结果
│   └── ...
└── production_config/                   # 生产配置分析结果
    └── ...
```

## 单文件分析示例

### 场景
分析当前目录下的单个日志文件 `accumulator.log`

### 使用方法
```bash
python run_simple.py
```

### 生成结果
```
当前目录/
├── accumulator.log                      # 原始日志
├── accumulator_data.csv                 # 结构化数据
├── feature_detailed_analysis.xlsx       # Excel详细报告
├── analysis_summary.txt                 # 分析摘要
├── charts/                              # 图表
│   ├── feature_type_distribution.png
│   ├── memory_usage_by_type.png
│   └── memory_efficiency.png
└── log_processor.log                    # 处理日志
```

## Excel报告内容说明

### 工作表结构
1. **总览** - 各Feature类型汇总统计
2. **全部数据** - 所有Feature完整数据（1803行 × 37列）
3. **DISTRIBUTION_详情** - 分布类型Feature详情
4. **RATIO_详情** - 比率类型Feature详情
5. **SEGMENT_详情** - 段类型Feature详情

### 主要数据列
- **基础信息**：Feature类型、FeatureID、管理器、时间戳
- **元素统计**：总元素数、段元素数、窗口元素数
- **内存信息**：总内存、内存池、开销内存、平均每元素内存
- **配置参数**：步长、步数、段长度、坐标信息
- **哈希表信息**：负载因子、桶数量、链长统计
- **内存池信息**：节点数、分配数、使用情况
- **计算列**：内存(MB)、内存效率(字节/元素)

## 分析对比示例

### 配置对比分析
通过批量分析，您可以轻松对比不同配置的性能：

1. **内存使用对比**
   - 查看各配置的总内存使用量
   - 对比不同Feature类型的内存分布
   - 识别内存使用异常的配置

2. **性能效率对比**
   - 对比平均每元素内存使用
   - 分析负载因子分布差异
   - 识别性能最优的配置

3. **Feature分布对比**
   - 对比各配置的Feature数量分布
   - 分析不同类型Feature的占比变化

### 实际使用流程
1. 将不同配置的日志文件放入一个文件夹
2. 使用有意义的文件名（如：baseline.log, optimized_v1.log）
3. 运行批量分析：`python run_batch.py <文件夹>`
4. 打开各子目录中的Excel文件进行详细对比
5. 参考图表进行可视化对比

## 注意事项

### 文件命名建议
- 使用有意义的文件名，如：
  - `baseline_config.log` - 基线配置
  - `memory_optimized.log` - 内存优化配置
  - `performance_tuned.log` - 性能调优配置
  - `production_20251219.log` - 生产环境日期标记

### 支持的文件格式
- `.log` 文件
- `.txt` 文件
- 文件内容必须是累加器日志格式

### 系统要求
- Python 3.6+
- 依赖包：pandas, numpy, matplotlib, openpyxl
- 安装命令：`pip install pandas numpy matplotlib openpyxl`

### 性能考虑
- 大文件处理可能需要较长时间
- 批量分析时建议不要同时处理过多文件
- 每个日志文件会生成独立的分析结果，占用相应的磁盘空间
