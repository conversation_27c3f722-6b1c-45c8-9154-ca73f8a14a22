{"analysis_timestamp": "2025-06-19T16:01:08.491807", "data_source": "accumulator_data.csv", "summary": {"total_features": 2034, "feature_types": 3, "managers": 2, "total_memory_gb": 26.306185863912106, "total_elements": 196632728.0}, "detailed_statistics": {"data_overview": {"total_records": 2034, "feature_types": {"RATIO": 1284, "SEGMENT": 400, "DISTRIBUTION": 350}, "managers": {"CLICK_CPC_LOG": 2014, "CLICK_CPM_LOG": 20}, "memory_available_count": {"true": 1803, "false": 231}}, "memory_statistics": {"total_memory_mean": 15666140.8718802, "total_memory_median": 12433168.0, "total_memory_std": 10970078.57905446, "total_memory_min": 11535620.0, "total_memory_max": 211298008.0, "total_memory_sum": 28246051992.0}, "element_statistics": {"total_elements_mean": 96672.92428711898, "total_elements_median": 7528.0, "total_elements_std": 349802.1349092112, "total_elements_min": 0.0, "total_elements_max": 8933695.0, "total_elements_sum": 196632728.0}, "performance_statistics": {"avg_per_element_mean": 169363.5210427066, "avg_per_element_median": 661.7, "avg_per_element_std": 708127.4253340635, "avg_per_element_min": 0.0, "avg_per_element_max": 6227814.0}}, "anomalies": {"memory_anomalies": [{"feature_id": 44235102, "feature_type": "RATIO", "total_memory": 31462496.0, "reason": "Memory usage outlier"}, {"feature_id": 47426021, "feature_type": "RATIO", "total_memory": 17362024.0, "reason": "Memory usage outlier"}, {"feature_id": 252026015, "feature_type": "SEGMENT", "total_memory": 51110576.0, "reason": "Memory usage outlier"}, {"feature_id": 252025015, "feature_type": "SEGMENT", "total_memory": 66263272.0, "reason": "Memory usage outlier"}, {"feature_id": 251026015, "feature_type": "SEGMENT", "total_memory": 32421364.0, "reason": "Memory usage outlier"}, {"feature_id": 47273011, "feature_type": "RATIO", "total_memory": 20108176.0, "reason": "Memory usage outlier"}, {"feature_id": 47274011, "feature_type": "RATIO", "total_memory": 20108176.0, "reason": "Memory usage outlier"}, {"feature_id": 52124101, "feature_type": "DISTRIBUTION", "total_memory": 19092600.0, "reason": "Memory usage outlier"}, {"feature_id": 383003402, "feature_type": "SEGMENT", "total_memory": 18072008.0, "reason": "Memory usage outlier"}, {"feature_id": 80024010, "feature_type": "DISTRIBUTION", "total_memory": 18127828.0, "reason": "Memory usage outlier"}, {"feature_id": 76901103, "feature_type": "RATIO", "total_memory": 24750068.0, "reason": "Memory usage outlier"}, {"feature_id": 39233001, "feature_type": "SEGMENT", "total_memory": 27018872.0, "reason": "Memory usage outlier"}, {"feature_id": 44235101, "feature_type": "RATIO", "total_memory": 33295040.0, "reason": "Memory usage outlier"}, {"feature_id": 38233001, "feature_type": "SEGMENT", "total_memory": 18541744.0, "reason": "Memory usage outlier"}, {"feature_id": 257024010, "feature_type": "SEGMENT", "total_memory": 17365956.0, "reason": "Memory usage outlier"}, {"feature_id": 256024010, "feature_type": "SEGMENT", "total_memory": 17718500.0, "reason": "Memory usage outlier"}, {"feature_id": 255024010, "feature_type": "SEGMENT", "total_memory": 17483632.0, "reason": "Memory usage outlier"}, {"feature_id": 257058010, "feature_type": "SEGMENT", "total_memory": 17565956.0, "reason": "Memory usage outlier"}, {"feature_id": 53030, "feature_type": "DISTRIBUTION", "total_memory": 20047468.0, "reason": "Memory usage outlier"}, {"feature_id": 194008013, "feature_type": "DISTRIBUTION", "total_memory": 40890808.0, "reason": "Memory usage outlier"}, {"feature_id": 55602, "feature_type": "DISTRIBUTION", "total_memory": 20051912.0, "reason": "Memory usage outlier"}, {"feature_id": 190008015, "feature_type": "DISTRIBUTION", "total_memory": 39918508.0, "reason": "Memory usage outlier"}, {"feature_id": 3030302, "feature_type": "SEGMENT", "total_memory": 32977200.0, "reason": "Memory usage outlier"}, {"feature_id": 96010008, "feature_type": "SEGMENT", "total_memory": 27015000.0, "reason": "Memory usage outlier"}, {"feature_id": 170140015, "feature_type": "RATIO", "total_memory": 24446824.0, "reason": "Memory usage outlier"}, {"feature_id": 5507301, "feature_type": "DISTRIBUTION", "total_memory": 30434244.0, "reason": "Memory usage outlier"}, {"feature_id": 77821102, "feature_type": "RATIO", "total_memory": 24114524.0, "reason": "Memory usage outlier"}, {"feature_id": 47193011, "feature_type": "RATIO", "total_memory": 26866972.0, "reason": "Memory usage outlier"}, {"feature_id": 96010001, "feature_type": "SEGMENT", "total_memory": 29532676.0, "reason": "Memory usage outlier"}, {"feature_id": 185024013, "feature_type": "RATIO", "total_memory": 25645276.0, "reason": "Memory usage outlier"}, {"feature_id": 3030301, "feature_type": "SEGMENT", "total_memory": 33177200.0, "reason": "Memory usage outlier"}, {"feature_id": 150140015, "feature_type": "RATIO", "total_memory": 24446824.0, "reason": "Memory usage outlier"}, {"feature_id": 7473601, "feature_type": "SEGMENT", "total_memory": 25792720.0, "reason": "Memory usage outlier"}, {"feature_id": 191000015, "feature_type": "DISTRIBUTION", "total_memory": 53190036.0, "reason": "Memory usage outlier"}, {"feature_id": 187025013, "feature_type": "RATIO", "total_memory": 27123728.0, "reason": "Memory usage outlier"}, {"feature_id": 5110801, "feature_type": "DISTRIBUTION", "total_memory": 21935680.0, "reason": "Memory usage outlier"}, {"feature_id": 41275105, "feature_type": "SEGMENT", "total_memory": 26062456.0, "reason": "Memory usage outlier"}, {"feature_id": 96072015, "feature_type": "DISTRIBUTION", "total_memory": 20980812.0, "reason": "Memory usage outlier"}, {"feature_id": 96072013, "feature_type": "DISTRIBUTION", "total_memory": 68497332.0, "reason": "Memory usage outlier"}, {"feature_id": 96072010, "feature_type": "DISTRIBUTION", "total_memory": 17186880.0, "reason": "Memory usage outlier"}, {"feature_id": 125008015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 96072007, "feature_type": "DISTRIBUTION", "total_memory": 55108488.0, "reason": "Memory usage outlier"}, {"feature_id": 96072001, "feature_type": "DISTRIBUTION", "total_memory": 72177332.0, "reason": "Memory usage outlier"}, {"feature_id": 191002013, "feature_type": "DISTRIBUTION", "total_memory": 21893924.0, "reason": "Memory usage outlier"}, {"feature_id": 96072018, "feature_type": "DISTRIBUTION", "total_memory": 21935680.0, "reason": "Memory usage outlier"}, {"feature_id": 96040001, "feature_type": "SEGMENT", "total_memory": 87852136.0, "reason": "Memory usage outlier"}, {"feature_id": 189002013, "feature_type": "DISTRIBUTION", "total_memory": 20973924.0, "reason": "Memory usage outlier"}, {"feature_id": 163025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 4718101, "feature_type": "RATIO", "total_memory": 24779124.0, "reason": "Memory usage outlier"}, {"feature_id": 96010007, "feature_type": "SEGMENT", "total_memory": 21510028.0, "reason": "Memory usage outlier"}, {"feature_id": 440000015, "feature_type": "RATIO", "total_memory": 26866972.0, "reason": "Memory usage outlier"}, {"feature_id": 96010004, "feature_type": "SEGMENT", "total_memory": 23066932.0, "reason": "Memory usage outlier"}, {"feature_id": 96010003, "feature_type": "SEGMENT", "total_memory": 27215000.0, "reason": "Memory usage outlier"}, {"feature_id": 75110201, "feature_type": "SEGMENT", "total_memory": 63881796.0, "reason": "Memory usage outlier"}, {"feature_id": 41110, "feature_type": "RATIO", "total_memory": 31104044.0, "reason": "Memory usage outlier"}, {"feature_id": 4662801, "feature_type": "RATIO", "total_memory": 21336652.0, "reason": "Memory usage outlier"}, {"feature_id": 50501, "feature_type": "DISTRIBUTION", "total_memory": 68384012.0, "reason": "Memory usage outlier"}, {"feature_id": 520415031, "feature_type": "DISTRIBUTION", "total_memory": 21935680.0, "reason": "Memory usage outlier"}, {"feature_id": 50504, "feature_type": "DISTRIBUTION", "total_memory": 72098880.0, "reason": "Memory usage outlier"}, {"feature_id": 7572601, "feature_type": "SEGMENT", "total_memory": 39762044.0, "reason": "Memory usage outlier"}, {"feature_id": 433101, "feature_type": "RATIO", "total_memory": 25668520.0, "reason": "Memory usage outlier"}, {"feature_id": 71050303, "feature_type": "SEGMENT", "total_memory": 59346568.0, "reason": "Memory usage outlier"}, {"feature_id": 71050302, "feature_type": "SEGMENT", "total_memory": 211298008.0, "reason": "Memory usage outlier"}, {"feature_id": 71050301, "feature_type": "SEGMENT", "total_memory": 59346568.0, "reason": "Memory usage outlier"}, {"feature_id": 192000013, "feature_type": "DISTRIBUTION", "total_memory": 57026940.0, "reason": "Memory usage outlier"}, {"feature_id": 32401, "feature_type": "SEGMENT", "total_memory": 21988480.0, "reason": "Memory usage outlier"}, {"feature_id": 924005, "feature_type": "RATIO", "total_memory": 32455040.0, "reason": "Memory usage outlier"}, {"feature_id": 472920101, "feature_type": "RATIO", "total_memory": 22223140.0, "reason": "Memory usage outlier"}, {"feature_id": 7572901, "feature_type": "SEGMENT", "total_memory": 40557784.0, "reason": "Memory usage outlier"}, {"feature_id": 96071006, "feature_type": "DISTRIBUTION", "total_memory": 19089248.0, "reason": "Memory usage outlier"}, {"feature_id": 4665601, "feature_type": "RATIO", "total_memory": 18816260.0, "reason": "Memory usage outlier"}, {"feature_id": 172008013, "feature_type": "SEGMENT", "total_memory": 19457968.0, "reason": "Memory usage outlier"}, {"feature_id": 41415101, "feature_type": "RATIO", "total_memory": 30657364.0, "reason": "Memory usage outlier"}, {"feature_id": 192008013, "feature_type": "DISTRIBUTION", "total_memory": 44657976.0, "reason": "Memory usage outlier"}, {"feature_id": 173025013, "feature_type": "RATIO", "total_memory": 27123728.0, "reason": "Memory usage outlier"}, {"feature_id": 41275101, "feature_type": "RATIO", "total_memory": 31182496.0, "reason": "Memory usage outlier"}, {"feature_id": 173000013, "feature_type": "RATIO", "total_memory": 26306972.0, "reason": "Memory usage outlier"}, {"feature_id": 151025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 5110701, "feature_type": "DISTRIBUTION", "total_memory": 21935680.0, "reason": "Memory usage outlier"}, {"feature_id": 172024013, "feature_type": "SEGMENT", "total_memory": 21965236.0, "reason": "Memory usage outlier"}, {"feature_id": 194000013, "feature_type": "DISTRIBUTION", "total_memory": 53190036.0, "reason": "Memory usage outlier"}, {"feature_id": 140080015, "feature_type": "RATIO", "total_memory": 21943140.0, "reason": "Memory usage outlier"}, {"feature_id": 41275103, "feature_type": "SEGMENT", "total_memory": 26062456.0, "reason": "Memory usage outlier"}, {"feature_id": 192002013, "feature_type": "DISTRIBUTION", "total_memory": 21893924.0, "reason": "Memory usage outlier"}, {"feature_id": 251025015, "feature_type": "SEGMENT", "total_memory": 38774392.0, "reason": "Memory usage outlier"}, {"feature_id": 310251, "feature_type": "SEGMENT", "total_memory": 25697324.0, "reason": "Memory usage outlier"}, {"feature_id": 57020101, "feature_type": "DISTRIBUTION", "total_memory": 90689564.0, "reason": "Memory usage outlier"}, {"feature_id": 1102280210015, "feature_type": "RATIO", "total_memory": 19524932.0, "reason": "Memory usage outlier"}, {"feature_id": 186025013, "feature_type": "RATIO", "total_memory": 27123728.0, "reason": "Memory usage outlier"}, {"feature_id": 508501, "feature_type": "DISTRIBUTION", "total_memory": 19090156.0, "reason": "Memory usage outlier"}, {"feature_id": 152025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 247024008, "feature_type": "DISTRIBUTION", "total_memory": 17182352.0, "reason": "Memory usage outlier"}, {"feature_id": 710504, "feature_type": "SEGMENT", "total_memory": 72546568.0, "reason": "Memory usage outlier"}, {"feature_id": 301101, "feature_type": "SEGMENT", "total_memory": 19223100.0, "reason": "Memory usage outlier"}, {"feature_id": 195024013, "feature_type": "DISTRIBUTION", "total_memory": 48479624.0, "reason": "Memory usage outlier"}, {"feature_id": 71310041, "feature_type": "SEGMENT", "total_memory": 26239212.0, "reason": "Memory usage outlier"}, {"feature_id": 96072016, "feature_type": "DISTRIBUTION", "total_memory": 20980812.0, "reason": "Memory usage outlier"}, {"feature_id": 4727401, "feature_type": "RATIO", "total_memory": 18773852.0, "reason": "Memory usage outlier"}, {"feature_id": 3088401, "feature_type": "SEGMENT", "total_memory": 80881332.0, "reason": "Memory usage outlier"}, {"feature_id": 3784101, "feature_type": "SEGMENT", "total_memory": 19370800.0, "reason": "Memory usage outlier"}, {"feature_id": 4719301, "feature_type": "RATIO", "total_memory": 21017912.0, "reason": "Memory usage outlier"}, {"feature_id": 31108, "feature_type": "SEGMENT", "total_memory": 25984004.0, "reason": "Memory usage outlier"}, {"feature_id": 540140015, "feature_type": "RATIO", "total_memory": 23378008.0, "reason": "Memory usage outlier"}, {"feature_id": 436101, "feature_type": "RATIO", "total_memory": 25030068.0, "reason": "Memory usage outlier"}, {"feature_id": 244052015, "feature_type": "RATIO", "total_memory": 17048448.0, "reason": "Memory usage outlier"}, {"feature_id": 4742601, "feature_type": "RATIO", "total_memory": 18536260.0, "reason": "Memory usage outlier"}, {"feature_id": 96072017, "feature_type": "DISTRIBUTION", "total_memory": 21935680.0, "reason": "Memory usage outlier"}, {"feature_id": 500230, "feature_type": "DISTRIBUTION", "total_memory": 20974356.0, "reason": "Memory usage outlier"}, {"feature_id": 50850104, "feature_type": "DISTRIBUTION", "total_memory": 19089248.0, "reason": "Memory usage outlier"}, {"feature_id": 96072008, "feature_type": "DISTRIBUTION", "total_memory": 55108488.0, "reason": "Memory usage outlier"}, {"feature_id": 189024013, "feature_type": "DISTRIBUTION", "total_memory": 55085244.0, "reason": "Memory usage outlier"}, {"feature_id": 65000015, "feature_type": "RATIO", "total_memory": 25030068.0, "reason": "Memory usage outlier"}, {"feature_id": 490140015, "feature_type": "RATIO", "total_memory": 23554524.0, "reason": "Memory usage outlier"}, {"feature_id": 96072009, "feature_type": "DISTRIBUTION", "total_memory": 17186880.0, "reason": "Memory usage outlier"}, {"feature_id": 195000013, "feature_type": "DISTRIBUTION", "total_memory": 53190036.0, "reason": "Memory usage outlier"}, {"feature_id": 96072014, "feature_type": "DISTRIBUTION", "total_memory": 68497332.0, "reason": "Memory usage outlier"}, {"feature_id": 174025013, "feature_type": "SEGMENT", "total_memory": 23043688.0, "reason": "Memory usage outlier"}, {"feature_id": 7473701, "feature_type": "SEGMENT", "total_memory": 25592720.0, "reason": "Memory usage outlier"}, {"feature_id": 45100101, "feature_type": "RATIO", "total_memory": 22538008.0, "reason": "Memory usage outlier"}, {"feature_id": 520415030, "feature_type": "DISTRIBUTION", "total_memory": 18139024.0, "reason": "Memory usage outlier"}, {"feature_id": 96072002, "feature_type": "DISTRIBUTION", "total_memory": 76777332.0, "reason": "Memory usage outlier"}, {"feature_id": 196008015, "feature_type": "DISTRIBUTION", "total_memory": 38998508.0, "reason": "Memory usage outlier"}, {"feature_id": 310241, "feature_type": "SEGMENT", "total_memory": 25697324.0, "reason": "Memory usage outlier"}, {"feature_id": 71310051, "feature_type": "SEGMENT", "total_memory": 137273072.0, "reason": "Memory usage outlier"}, {"feature_id": 3783101, "feature_type": "SEGMENT", "total_memory": 18107188.0, "reason": "Memory usage outlier"}, {"feature_id": 170240015, "feature_type": "RATIO", "total_memory": 24446824.0, "reason": "Memory usage outlier"}, {"feature_id": 650010015, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 197024013, "feature_type": "DISTRIBUTION", "total_memory": 18143420.0, "reason": "Memory usage outlier"}, {"feature_id": 1102280070015, "feature_type": "RATIO", "total_memory": 27146972.0, "reason": "Memory usage outlier"}, {"feature_id": 75110301, "feature_type": "SEGMENT", "total_memory": 25697324.0, "reason": "Memory usage outlier"}, {"feature_id": 194024013, "feature_type": "DISTRIBUTION", "total_memory": 48479624.0, "reason": "Memory usage outlier"}, {"feature_id": 36030101, "feature_type": "SEGMENT", "total_memory": 22588480.0, "reason": "Memory usage outlier"}, {"feature_id": 47181021, "feature_type": "RATIO", "total_memory": 25590068.0, "reason": "Memory usage outlier"}, {"feature_id": 194002015, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 172025013, "feature_type": "SEGMENT", "total_memory": 23043688.0, "reason": "Memory usage outlier"}, {"feature_id": 194000015, "feature_type": "DISTRIBUTION", "total_memory": 52270036.0, "reason": "Memory usage outlier"}, {"feature_id": 172000013, "feature_type": "SEGMENT", "total_memory": 22466932.0, "reason": "Memory usage outlier"}, {"feature_id": 469201, "feature_type": "RATIO", "total_memory": 20718540.0, "reason": "Memory usage outlier"}, {"feature_id": 420080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 10250015, "feature_type": "SEGMENT", "total_memory": 22188480.0, "reason": "Memory usage outlier"}, {"feature_id": 490080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 1102260240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 700210015, "feature_type": "RATIO", "total_memory": 19524932.0, "reason": "Memory usage outlier"}, {"feature_id": 186000013, "feature_type": "RATIO", "total_memory": 26306972.0, "reason": "Memory usage outlier"}, {"feature_id": 193000013, "feature_type": "DISTRIBUTION", "total_memory": 52270036.0, "reason": "Memory usage outlier"}, {"feature_id": 535001, "feature_type": "DISTRIBUTION", "total_memory": 17188668.0, "reason": "Memory usage outlier"}, {"feature_id": 470080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 125057015, "feature_type": "RATIO", "total_memory": 16715824.0, "reason": "Memory usage outlier"}, {"feature_id": 191052015, "feature_type": "DISTRIBUTION", "total_memory": 28568416.0, "reason": "Memory usage outlier"}, {"feature_id": 420140015, "feature_type": "RATIO", "total_memory": 23554524.0, "reason": "Memory usage outlier"}, {"feature_id": 190000015, "feature_type": "DISTRIBUTION", "total_memory": 53190036.0, "reason": "Memory usage outlier"}, {"feature_id": 186024013, "feature_type": "RATIO", "total_memory": 25645276.0, "reason": "Memory usage outlier"}, {"feature_id": 198024013, "feature_type": "DISTRIBUTION", "total_memory": 18143420.0, "reason": "Memory usage outlier"}, {"feature_id": 189000013, "feature_type": "DISTRIBUTION", "total_memory": 56948488.0, "reason": "Memory usage outlier"}, {"feature_id": 155025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 190001015, "feature_type": "DISTRIBUTION", "total_memory": 57868488.0, "reason": "Memory usage outlier"}, {"feature_id": 191024013, "feature_type": "DISTRIBUTION", "total_memory": 55085244.0, "reason": "Memory usage outlier"}, {"feature_id": 550030015, "feature_type": "RATIO", "total_memory": 21330840.0, "reason": "Memory usage outlier"}, {"feature_id": 193024013, "feature_type": "DISTRIBUTION", "total_memory": 48479624.0, "reason": "Memory usage outlier"}, {"feature_id": 195008013, "feature_type": "DISTRIBUTION", "total_memory": 40890808.0, "reason": "Memory usage outlier"}, {"feature_id": 187000013, "feature_type": "RATIO", "total_memory": 26306972.0, "reason": "Memory usage outlier"}, {"feature_id": 161025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 569121, "feature_type": "DISTRIBUTION", "total_memory": 48361220.0, "reason": "Memory usage outlier"}, {"feature_id": 610230015, "feature_type": "RATIO", "total_memory": 25925276.0, "reason": "Memory usage outlier"}, {"feature_id": 71700021, "feature_type": "SEGMENT", "total_memory": 20353128.0, "reason": "Memory usage outlier"}, {"feature_id": 173008013, "feature_type": "RATIO", "total_memory": 22258008.0, "reason": "Memory usage outlier"}, {"feature_id": 140240015, "feature_type": "RATIO", "total_memory": 24446824.0, "reason": "Memory usage outlier"}, {"feature_id": 185008013, "feature_type": "RATIO", "total_memory": 22258008.0, "reason": "Memory usage outlier"}, {"feature_id": 150070015, "feature_type": "RATIO", "total_memory": 27146972.0, "reason": "Memory usage outlier"}, {"feature_id": 191000013, "feature_type": "DISTRIBUTION", "total_memory": 57026940.0, "reason": "Memory usage outlier"}, {"feature_id": 173052013, "feature_type": "RATIO", "total_memory": 17647188.0, "reason": "Memory usage outlier"}, {"feature_id": 31010, "feature_type": "RATIO", "total_memory": 31104044.0, "reason": "Memory usage outlier"}, {"feature_id": 390880430, "feature_type": "SEGMENT", "total_memory": 19141744.0, "reason": "Memory usage outlier"}, {"feature_id": 191002015, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 166025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 31018, "feature_type": "RATIO", "total_memory": 29984044.0, "reason": "Memory usage outlier"}, {"feature_id": 47091, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 41023304, "feature_type": "RATIO", "total_memory": 25668520.0, "reason": "Memory usage outlier"}, {"feature_id": 4402, "feature_type": "RATIO", "total_memory": 18567256.0, "reason": "Memory usage outlier"}, {"feature_id": 31021, "feature_type": "RATIO", "total_memory": 30824044.0, "reason": "Memory usage outlier"}, {"feature_id": 50850103, "feature_type": "DISTRIBUTION", "total_memory": 19090156.0, "reason": "Memory usage outlier"}, {"feature_id": 185025013, "feature_type": "RATIO", "total_memory": 27123728.0, "reason": "Memory usage outlier"}, {"feature_id": 50850301, "feature_type": "DISTRIBUTION", "total_memory": 17185252.0, "reason": "Memory usage outlier"}, {"feature_id": 50850105, "feature_type": "DISTRIBUTION", "total_memory": 18143420.0, "reason": "Memory usage outlier"}, {"feature_id": 1102260000015, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 140025012, "feature_type": "SEGMENT", "total_memory": 29532676.0, "reason": "Memory usage outlier"}, {"feature_id": 141025012, "feature_type": "SEGMENT", "total_memory": 29532676.0, "reason": "Memory usage outlier"}, {"feature_id": 194008015, "feature_type": "DISTRIBUTION", "total_memory": 38998508.0, "reason": "Memory usage outlier"}, {"feature_id": 150025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 153025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 154025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 156025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 157025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 158025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 159025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 160025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 162025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 164025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 165025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 3030306, "feature_type": "SEGMENT", "total_memory": 58328732.0, "reason": "Memory usage outlier"}, {"feature_id": 167025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 168025012, "feature_type": "RATIO", "total_memory": 35932716.0, "reason": "Memory usage outlier"}, {"feature_id": 430080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 256058010, "feature_type": "SEGMENT", "total_memory": 34695940.0, "reason": "Memory usage outlier"}, {"feature_id": 41023301, "feature_type": "RATIO", "total_memory": 24750068.0, "reason": "Memory usage outlier"}, {"feature_id": 550140015, "feature_type": "RATIO", "total_memory": 23378008.0, "reason": "Memory usage outlier"}, {"feature_id": 190008013, "feature_type": "DISTRIBUTION", "total_memory": 44657976.0, "reason": "Memory usage outlier"}, {"feature_id": 193002013, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 65052015, "feature_type": "RATIO", "total_memory": 17048448.0, "reason": "Memory usage outlier"}, {"feature_id": 189052013, "feature_type": "DISTRIBUTION", "total_memory": 31367156.0, "reason": "Memory usage outlier"}, {"feature_id": 185052013, "feature_type": "RATIO", "total_memory": 17927188.0, "reason": "Memory usage outlier"}, {"feature_id": 191008013, "feature_type": "DISTRIBUTION", "total_memory": 44657976.0, "reason": "Memory usage outlier"}, {"feature_id": 196002013, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 186008013, "feature_type": "RATIO", "total_memory": 22258008.0, "reason": "Memory usage outlier"}, {"feature_id": 193008013, "feature_type": "DISTRIBUTION", "total_memory": 40890808.0, "reason": "Memory usage outlier"}, {"feature_id": 195002013, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 190052013, "feature_type": "DISTRIBUTION", "total_memory": 31367156.0, "reason": "Memory usage outlier"}, {"feature_id": 330730, "feature_type": "SEGMENT", "total_memory": 27097324.0, "reason": "Memory usage outlier"}, {"feature_id": 186052013, "feature_type": "RATIO", "total_memory": 17927188.0, "reason": "Memory usage outlier"}, {"feature_id": 173039013, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 252025005, "feature_type": "SEGMENT", "total_memory": 16675128.0, "reason": "Memory usage outlier"}, {"feature_id": 196000013, "feature_type": "DISTRIBUTION", "total_memory": 53190036.0, "reason": "Memory usage outlier"}, {"feature_id": 190000013, "feature_type": "DISTRIBUTION", "total_memory": 57026940.0, "reason": "Memory usage outlier"}, {"feature_id": 187024013, "feature_type": "RATIO", "total_memory": 25365276.0, "reason": "Memory usage outlier"}, {"feature_id": 433201, "feature_type": "RATIO", "total_memory": 25668520.0, "reason": "Memory usage outlier"}, {"feature_id": 196024013, "feature_type": "DISTRIBUTION", "total_memory": 48479624.0, "reason": "Memory usage outlier"}, {"feature_id": 194002013, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 1102290240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 192024013, "feature_type": "DISTRIBUTION", "total_memory": 55085244.0, "reason": "Memory usage outlier"}, {"feature_id": 190002013, "feature_type": "DISTRIBUTION", "total_memory": 20973924.0, "reason": "Memory usage outlier"}, {"feature_id": 185000013, "feature_type": "RATIO", "total_memory": 26306972.0, "reason": "Memory usage outlier"}, {"feature_id": 640240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 3830013002, "feature_type": "SEGMENT", "total_memory": 24766572.0, "reason": "Memory usage outlier"}, {"feature_id": 189008013, "feature_type": "DISTRIBUTION", "total_memory": 44657976.0, "reason": "Memory usage outlier"}, {"feature_id": 430030015, "feature_type": "RATIO", "total_memory": 21330840.0, "reason": "Memory usage outlier"}, {"feature_id": 410224401, "feature_type": "RATIO", "total_memory": 66826940.0, "reason": "Memory usage outlier"}, {"feature_id": 410080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 10310015, "feature_type": "SEGMENT", "total_memory": 22388480.0, "reason": "Memory usage outlier"}, {"feature_id": 190024013, "feature_type": "DISTRIBUTION", "total_memory": 55085244.0, "reason": "Memory usage outlier"}, {"feature_id": 96042001, "feature_type": "SEGMENT", "total_memory": 49476196.0, "reason": "Memory usage outlier"}, {"feature_id": 410010015, "feature_type": "RATIO", "total_memory": 27146972.0, "reason": "Memory usage outlier"}, {"feature_id": 1102220010015, "feature_type": "RATIO", "total_memory": 28546972.0, "reason": "Memory usage outlier"}, {"feature_id": 1102290010015, "feature_type": "RATIO", "total_memory": 28546972.0, "reason": "Memory usage outlier"}, {"feature_id": 580080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 41265101, "feature_type": "RATIO", "total_memory": 22258008.0, "reason": "Memory usage outlier"}, {"feature_id": 1102230230015, "feature_type": "RATIO", "total_memory": 26866972.0, "reason": "Memory usage outlier"}, {"feature_id": 196024015, "feature_type": "DISTRIBUTION", "total_memory": 46463108.0, "reason": "Memory usage outlier"}, {"feature_id": 680080015, "feature_type": "RATIO", "total_memory": 21050840.0, "reason": "Memory usage outlier"}, {"feature_id": 680140015, "feature_type": "RATIO", "total_memory": 23834524.0, "reason": "Memory usage outlier"}, {"feature_id": 580030015, "feature_type": "RATIO", "total_memory": 21315344.0, "reason": "Memory usage outlier"}, {"feature_id": 700000015, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 1102220240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 200010015, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 1102250240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 460140015, "feature_type": "RATIO", "total_memory": 23378008.0, "reason": "Memory usage outlier"}, {"feature_id": 490030015, "feature_type": "RATIO", "total_memory": 21330840.0, "reason": "Memory usage outlier"}, {"feature_id": 570140015, "feature_type": "RATIO", "total_memory": 23378008.0, "reason": "Memory usage outlier"}, {"feature_id": 650240015, "feature_type": "RATIO", "total_memory": 24726824.0, "reason": "Memory usage outlier"}, {"feature_id": 690000015, "feature_type": "RATIO", "total_memory": 28266972.0, "reason": "Memory usage outlier"}, {"feature_id": 200050015, "feature_type": "RATIO", "total_memory": 30544044.0, "reason": "Memory usage outlier"}, {"feature_id": 160000015, "feature_type": "RATIO", "total_memory": 27706972.0, "reason": "Memory usage outlier"}, {"feature_id": 160030015, "feature_type": "RATIO", "total_memory": 21663140.0, "reason": "Memory usage outlier"}, {"feature_id": 170030015, "feature_type": "RATIO", "total_memory": 21663140.0, "reason": "Memory usage outlier"}, {"feature_id": 180230015, "feature_type": "RATIO", "total_memory": 26205276.0, "reason": "Memory usage outlier"}, {"feature_id": 630240015, "feature_type": "RATIO", "total_memory": 24114524.0, "reason": "Memory usage outlier"}, {"feature_id": 196008013, "feature_type": "DISTRIBUTION", "total_memory": 40890808.0, "reason": "Memory usage outlier"}, {"feature_id": 125024015, "feature_type": "RATIO", "total_memory": 23519656.0, "reason": "Memory usage outlier"}, {"feature_id": 41025102, "feature_type": "RATIO", "total_memory": 25668520.0, "reason": "Memory usage outlier"}, {"feature_id": 41033402, "feature_type": "SEGMENT", "total_memory": 133826672.0, "reason": "Memory usage outlier"}, {"feature_id": 4727301, "feature_type": "RATIO", "total_memory": 18772492.0, "reason": "Memory usage outlier"}, {"feature_id": 173024013, "feature_type": "RATIO", "total_memory": 25645276.0, "reason": "Memory usage outlier"}, {"feature_id": 61008015, "feature_type": "RATIO", "total_memory": 20718540.0, "reason": "Memory usage outlier"}, {"feature_id": 41265102, "feature_type": "RATIO", "total_memory": 27986972.0, "reason": "Memory usage outlier"}, {"feature_id": 172001015, "feature_type": "SEGMENT", "total_memory": 22588480.0, "reason": "Memory usage outlier"}, {"feature_id": 36030201, "feature_type": "SEGMENT", "total_memory": 28667300.0, "reason": "Memory usage outlier"}, {"feature_id": 390880431, "feature_type": "SEGMENT", "total_memory": 28215000.0, "reason": "Memory usage outlier"}, {"feature_id": 3030305, "feature_type": "SEGMENT", "total_memory": 58328732.0, "reason": "Memory usage outlier"}, {"feature_id": 1102260080015, "feature_type": "RATIO", "total_memory": 21943140.0, "reason": "Memory usage outlier"}, {"feature_id": 70053030, "feature_type": "SEGMENT", "total_memory": 38433232.0, "reason": "Memory usage outlier"}, {"feature_id": 244000015, "feature_type": "RATIO", "total_memory": 25030068.0, "reason": "Memory usage outlier"}, {"feature_id": 244001015, "feature_type": "RATIO", "total_memory": 26508520.0, "reason": "Memory usage outlier"}, {"feature_id": 1102250210015, "feature_type": "RATIO", "total_memory": 19524932.0, "reason": "Memory usage outlier"}, {"feature_id": 172008015, "feature_type": "SEGMENT", "total_memory": 18318500.0, "reason": "Memory usage outlier"}, {"feature_id": 61024015, "feature_type": "RATIO", "total_memory": 23063140.0, "reason": "Memory usage outlier"}, {"feature_id": 61001015, "feature_type": "RATIO", "total_memory": 26508520.0, "reason": "Memory usage outlier"}, {"feature_id": 191001015, "feature_type": "DISTRIBUTION", "total_memory": 56948488.0, "reason": "Memory usage outlier"}, {"feature_id": 191008015, "feature_type": "DISTRIBUTION", "total_memory": 38998508.0, "reason": "Memory usage outlier"}, {"feature_id": 190002015, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 190052015, "feature_type": "DISTRIBUTION", "total_memory": 28568416.0, "reason": "Memory usage outlier"}, {"feature_id": 194024015, "feature_type": "DISTRIBUTION", "total_memory": 46463108.0, "reason": "Memory usage outlier"}, {"feature_id": 196002015, "feature_type": "DISTRIBUTION", "total_memory": 19082268.0, "reason": "Memory usage outlier"}, {"feature_id": 196000015, "feature_type": "DISTRIBUTION", "total_memory": 52270036.0, "reason": "Memory usage outlier"}, {"feature_id": 550711130, "feature_type": "DISTRIBUTION", "total_memory": 93742660.0, "reason": "Memory usage outlier"}], "performance_anomalies": [{"feature_id": 1575670004, "feature_type": "SEGMENT", "avg_per_element": 1955936.67, "reason": "High memory per element"}, {"feature_id": 5102201, "feature_type": "DISTRIBUTION", "avg_per_element": 1245562.8, "reason": "High memory per element"}, {"feature_id": 4688002, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 58016009, "feature_type": "RATIO", "avg_per_element": 1687951.43, "reason": "High memory per element"}, {"feature_id": 60016009, "feature_type": "RATIO", "avg_per_element": 1687951.43, "reason": "High memory per element"}, {"feature_id": 64016009, "feature_type": "RATIO", "avg_per_element": 2953915.0, "reason": "High memory per element"}, {"feature_id": 41285101, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 25016009, "feature_type": "RATIO", "avg_per_element": 1476957.5, "reason": "High memory per element"}, {"feature_id": 53016009, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 23018008, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 63020008, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 22017008, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 28017008, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 33017008, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 56020008, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 61017008, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 25020008, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 61018008, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 20017008, "feature_type": "RATIO", "avg_per_element": 1312851.11, "reason": "High memory per element"}, {"feature_id": 45608, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 59017008, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 4099601, "feature_type": "RATIO", "avg_per_element": 2953915.0, "reason": "High memory per element"}, {"feature_id": 3041511, "feature_type": "SEGMENT", "avg_per_element": 2347124.0, "reason": "High memory per element"}, {"feature_id": 4041521, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 4099301, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 40312230, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 69016012, "feature_type": "RATIO", "avg_per_element": 3938553.33, "reason": "High memory per element"}, {"feature_id": 65020008, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 32018008, "feature_type": "RATIO", "avg_per_element": 908896.92, "reason": "High memory per element"}, {"feature_id": 16016009, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 56016009, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 66016009, "feature_type": "RATIO", "avg_per_element": 1312851.11, "reason": "High memory per element"}, {"feature_id": 40230, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 47601, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 22016009, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 48017008, "feature_type": "RATIO", "avg_per_element": 1181566.0, "reason": "High memory per element"}, {"feature_id": 40130, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 65016009, "feature_type": "RATIO", "avg_per_element": 1312851.11, "reason": "High memory per element"}, {"feature_id": 17016009, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 70016009, "feature_type": "RATIO", "avg_per_element": 1312851.11, "reason": "High memory per element"}, {"feature_id": 49016009, "feature_type": "RATIO", "avg_per_element": 1476957.5, "reason": "High memory per element"}, {"feature_id": 31107, "feature_type": "RATIO", "avg_per_element": 3938553.33, "reason": "High memory per element"}, {"feature_id": 14016009, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 41001302, "feature_type": "RATIO", "avg_per_element": 1312851.11, "reason": "High memory per element"}, {"feature_id": 67020008, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 372380201, "feature_type": "SEGMENT", "avg_per_element": 1676517.14, "reason": "High memory per element"}, {"feature_id": 51121201, "feature_type": "DISTRIBUTION", "avg_per_element": 3113907.0, "reason": "High memory per element"}, {"feature_id": 454002, "feature_type": "RATIO", "avg_per_element": 1476957.5, "reason": "High memory per element"}, {"feature_id": 18016009, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 747250201, "feature_type": "SEGMENT", "avg_per_element": 1955936.67, "reason": "High memory per element"}, {"feature_id": 52016009, "feature_type": "RATIO", "avg_per_element": 1476957.5, "reason": "High memory per element"}, {"feature_id": 42016009, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 29016009, "feature_type": "RATIO", "avg_per_element": 908896.92, "reason": "High memory per element"}, {"feature_id": 19016009, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 4081701, "feature_type": "RATIO", "avg_per_element": 2953915.0, "reason": "High memory per element"}, {"feature_id": 45708, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 41016009, "feature_type": "RATIO", "avg_per_element": 1476957.5, "reason": "High memory per element"}, {"feature_id": 3041521, "feature_type": "SEGMENT", "avg_per_element": 1955936.67, "reason": "High memory per element"}, {"feature_id": 23016009, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 4784201, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 50960701, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 39020008, "feature_type": "RATIO", "avg_per_element": 3938553.33, "reason": "High memory per element"}, {"feature_id": 68018008, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 66016012, "feature_type": "RATIO", "avg_per_element": 2953915.0, "reason": "High memory per element"}, {"feature_id": 39016012, "feature_type": "RATIO", "avg_per_element": 1687951.43, "reason": "High memory per element"}, {"feature_id": 13016012, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 57016012, "feature_type": "RATIO", "avg_per_element": 2953915.0, "reason": "High memory per element"}, {"feature_id": 67016012, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 65016012, "feature_type": "RATIO", "avg_per_element": 1074150.91, "reason": "High memory per element"}, {"feature_id": 36016012, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 70016012, "feature_type": "RATIO", "avg_per_element": 984638.33, "reason": "High memory per element"}, {"feature_id": 38016012, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 33016012, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 4081301, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 57237011, "feature_type": "DISTRIBUTION", "avg_per_element": 889687.71, "reason": "High memory per element"}, {"feature_id": 100016009, "feature_type": "DISTRIBUTION", "avg_per_element": 6227814.0, "reason": "High memory per element"}, {"feature_id": 58018008, "feature_type": "RATIO", "avg_per_element": 908896.92, "reason": "High memory per element"}, {"feature_id": 4041511, "feature_type": "RATIO", "avg_per_element": 2363132.0, "reason": "High memory per element"}, {"feature_id": 41285102, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 300201, "feature_type": "SEGMENT", "avg_per_element": 1066874.55, "reason": "High memory per element"}, {"feature_id": 5724702, "feature_type": "DISTRIBUTION", "avg_per_element": 4151876.0, "reason": "High memory per element"}, {"feature_id": 304030202, "feature_type": "SEGMENT", "avg_per_element": 2933921.0, "reason": "High memory per element"}, {"feature_id": 41445102, "feature_type": "SEGMENT", "avg_per_element": 5867810.0, "reason": "High memory per element"}, {"feature_id": 28016012, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 18016012, "feature_type": "RATIO", "avg_per_element": 1969276.67, "reason": "High memory per element"}, {"feature_id": 30016012, "feature_type": "RATIO", "avg_per_element": 3938553.33, "reason": "High memory per element"}, {"feature_id": 64016012, "feature_type": "RATIO", "avg_per_element": 5907830.0, "reason": "High memory per element"}, {"feature_id": 6000501, "feature_type": "DISTRIBUTION", "avg_per_element": 2491381.6, "reason": "High memory per element"}, {"feature_id": 32533, "feature_type": "SEGMENT", "avg_per_element": 1955936.67, "reason": "High memory per element"}, {"feature_id": 6000601, "feature_type": "DISTRIBUTION", "avg_per_element": 4152302.67, "reason": "High memory per element"}, {"feature_id": 32633, "feature_type": "SEGMENT", "avg_per_element": 2933905.0, "reason": "High memory per element"}], "load_factor_anomalies": []}, "recommendations": ["发现91个性能异常特征，建议检查内存使用效率"]}