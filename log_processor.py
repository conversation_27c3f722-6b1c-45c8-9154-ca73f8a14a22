#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志处理脚本 - 将accumulator.log转换为结构化CSV格式

功能：
1. 解析accumulator.log文件
2. 提取特征统计信息
3. 清理和标准化数据
4. 导出为CSV格式
5. 错误处理和日志记录

作者：AI Assistant
日期：2025-06-19
"""

import re
import csv
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd


class LogProcessor:
    """日志处理器类"""
    
    def __init__(self, log_file: str, output_file: str = "accumulator_data.csv"):
        """
        初始化日志处理器
        
        Args:
            log_file: 输入日志文件路径
            output_file: 输出CSV文件路径
        """
        self.log_file = log_file
        self.output_file = output_file
        self.setup_logging()
        
        # 正则表达式模式
        self.patterns = {
            'timestamp': r'(\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
            'feature_basic': r'Feature: ID=(\d+), Type=(\w+), Elements=(\d+)(?:\(Seg=(\d+),Win=(\d+)\))?, Memory\(Total=(\d+)(?:,Pool=(\d+),Overhead=(\d+),AvgPerElement=([\d.]+))?\)',
            'feature_memory_na': r'Feature: ID=(\d+), Type=(\w+), Elements=(\d+), Memory=N/A',
            'manager': r'Manager \[([^\]]+)\]:',
            'monitor_header': r'=== FeatureAccumulatorCollector Monitor === Time: ([\d-]+ [\d:]+)',
            'coords': r'Coords\[StepLen=(\d+),StepNum=(\d+),MaxStepNum=(\d+),SegmentLen=(\d+),OldestCoord=(\d+),SegmentStartCoord=(\d+),LatestCoord=(\d+)\]',
            'segment_info': r'Segment\[Size=(\d+),BucketCount=(\d+),LoadFactor=([\d.]+),HashMap\[Size=(\d+),Buckets=(\d+),LoadFactor=([\d.]+),EmptyBuckets=(\d+),NonEmptyBuckets=(\d+),MaxChainLen=(\d+),AvgChainLen=([\d.]+)',
            'window_steps': r'WindowSteps\[Count=(\d+),Steps=\[([^\]]+)\]\]',
            'mempool': r'MemPool\[NodeNum=(\d+),MallocNum=(\d+),UsedBlocks=(\d+),MemConsume=(\d+),ActualNeed=(\d+),ActualUsed=(\d+)\]'
        }
        
    def setup_logging(self):
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('log_processor.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def parse_timestamp(self, line: str) -> Optional[str]:
        """解析时间戳"""
        match = re.search(self.patterns['timestamp'], line)
        if match:
            # 添加年份（假设为2025年）
            return f"2025-{match.group(1)}"
        return None
        
    def parse_feature_basic(self, line: str) -> Optional[Dict]:
        """解析基础特征信息"""
        # 尝试匹配有内存详情的特征
        match = re.search(self.patterns['feature_basic'], line)
        if match:
            return {
                'feature_id': int(match.group(1)),
                'feature_type': match.group(2),
                'total_elements': int(match.group(3)),
                'segment_elements': int(match.group(4)) if match.group(4) else None,
                'window_elements': int(match.group(5)) if match.group(5) else None,
                'total_memory': int(match.group(6)),
                'pool_memory': int(match.group(7)) if match.group(7) else None,
                'overhead_memory': int(match.group(8)) if match.group(8) else None,
                'avg_per_element': float(match.group(9)) if match.group(9) else None,
                'memory_available': True
            }
            
        # 尝试匹配Memory=N/A的特征
        match = re.search(self.patterns['feature_memory_na'], line)
        if match:
            return {
                'feature_id': int(match.group(1)),
                'feature_type': match.group(2),
                'total_elements': int(match.group(3)),
                'segment_elements': None,
                'window_elements': None,
                'total_memory': None,
                'pool_memory': None,
                'overhead_memory': None,
                'avg_per_element': None,
                'memory_available': False
            }
        return None
        
    def parse_detailed_info(self, line: str) -> Dict:
        """解析详细信息"""
        details = {}
        
        # 解析坐标信息
        coords_match = re.search(self.patterns['coords'], line)
        if coords_match:
            details.update({
                'step_len': int(coords_match.group(1)),
                'step_num': int(coords_match.group(2)),
                'max_step_num': int(coords_match.group(3)),
                'segment_len': int(coords_match.group(4)),
                'oldest_coord': int(coords_match.group(5)),
                'segment_start_coord': int(coords_match.group(6)),
                'latest_coord': int(coords_match.group(7))
            })
            
        # 解析段信息
        segment_match = re.search(self.patterns['segment_info'], line)
        if segment_match:
            details.update({
                'segment_size': int(segment_match.group(1)),
                'bucket_count': int(segment_match.group(2)),
                'load_factor': float(segment_match.group(3)),
                'hashmap_size': int(segment_match.group(4)),
                'hashmap_buckets': int(segment_match.group(5)),
                'hashmap_load_factor': float(segment_match.group(6)),
                'empty_buckets': int(segment_match.group(7)),
                'non_empty_buckets': int(segment_match.group(8)),
                'max_chain_len': int(segment_match.group(9)),
                'avg_chain_len': float(segment_match.group(10))
            })
            
        # 解析窗口步骤
        window_match = re.search(self.patterns['window_steps'], line)
        if window_match:
            steps_str = window_match.group(2)
            # 解析步骤大小
            step_sizes = []
            for step in re.findall(r'Step\d+\(Size=(\d+)\)', steps_str):
                step_sizes.append(int(step))
            details['window_step_sizes'] = step_sizes
            details['window_step_count'] = int(window_match.group(1))
            
        # 解析内存池信息
        mempool_match = re.search(self.patterns['mempool'], line)
        if mempool_match:
            details.update({
                'node_num': int(mempool_match.group(1)),
                'malloc_num': int(mempool_match.group(2)),
                'used_blocks': int(mempool_match.group(3)),
                'mem_consume': int(mempool_match.group(4)),
                'actual_need': int(mempool_match.group(5)),
                'actual_used': int(mempool_match.group(6))
            })
            
        return details
        
    def process_log_file(self) -> List[Dict]:
        """处理日志文件"""
        self.logger.info(f"开始处理日志文件: {self.log_file}")
        
        records = []
        current_timestamp = None
        current_manager = None
        current_feature = None
        line_count = 0
        error_count = 0
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line_count += 1
                    line = line.strip()
                    
                    if not line:
                        continue
                        
                    try:
                        # 解析时间戳
                        timestamp = self.parse_timestamp(line)
                        if timestamp:
                            current_timestamp = timestamp
                            
                        # 解析管理器信息
                        manager_match = re.search(self.patterns['manager'], line)
                        if manager_match:
                            current_manager = manager_match.group(1)
                            
                        # 解析特征基础信息
                        feature_info = self.parse_feature_basic(line)
                        if feature_info:
                            current_feature = feature_info.copy()
                            current_feature['timestamp'] = current_timestamp
                            current_feature['manager'] = current_manager
                            current_feature['line_number'] = line_num
                            
                        # 解析详细信息（Details行）
                        if current_feature and 'Details:' in line:
                            details = self.parse_detailed_info(line)
                            current_feature.update(details)
                            records.append(current_feature)
                            current_feature = None
                            
                        # 处理没有Details的特征（Memory=N/A的情况）
                        elif current_feature and current_feature.get('memory_available') is False:
                            records.append(current_feature)
                            current_feature = None
                            
                    except Exception as e:
                        error_count += 1
                        self.logger.warning(f"处理第{line_num}行时出错: {str(e)}")
                        continue
                        
        except FileNotFoundError:
            self.logger.error(f"文件未找到: {self.log_file}")
            raise
        except Exception as e:
            self.logger.error(f"读取文件时出错: {str(e)}")
            raise
            
        self.logger.info(f"处理完成: 总行数={line_count}, 提取记录数={len(records)}, 错误数={error_count}")
        return records
        
    def save_to_csv(self, records: List[Dict]):
        """保存数据到CSV文件"""
        if not records:
            self.logger.warning("没有数据可保存")
            return
            
        self.logger.info(f"保存{len(records)}条记录到CSV文件: {self.output_file}")
        
        # 创建DataFrame
        df = pd.DataFrame(records)
        
        # 重新排列列的顺序
        column_order = [
            'timestamp', 'manager', 'feature_id', 'feature_type', 'line_number',
            'total_elements', 'segment_elements', 'window_elements',
            'total_memory', 'pool_memory', 'overhead_memory', 'avg_per_element', 'memory_available',
            'step_len', 'step_num', 'max_step_num', 'segment_len',
            'oldest_coord', 'segment_start_coord', 'latest_coord',
            'segment_size', 'bucket_count', 'load_factor',
            'hashmap_size', 'hashmap_buckets', 'hashmap_load_factor',
            'empty_buckets', 'non_empty_buckets', 'max_chain_len', 'avg_chain_len',
            'window_step_count', 'window_step_sizes',
            'node_num', 'malloc_num', 'used_blocks', 'mem_consume', 'actual_need', 'actual_used'
        ]
        
        # 确保所有列都存在
        for col in column_order:
            if col not in df.columns:
                df[col] = None
                
        df = df[column_order]
        
        # 保存到CSV
        df.to_csv(self.output_file, index=False, encoding='utf-8-sig')
        self.logger.info(f"CSV文件保存成功: {self.output_file}")
        
        # 打印统计信息
        self.print_statistics(df)
        
    def print_statistics(self, df: pd.DataFrame):
        """打印数据统计信息"""
        print("\n=== 数据统计信息 ===")
        print(f"总记录数: {len(df)}")
        print(f"特征类型分布:")
        print(df['feature_type'].value_counts())
        print(f"\n管理器分布:")
        print(df['manager'].value_counts())
        print(f"\n内存可用性:")
        print(df['memory_available'].value_counts())
        
    def run(self):
        """运行完整的处理流程"""
        try:
            records = self.process_log_file()
            self.save_to_csv(records)
            print(f"\n处理完成！CSV文件已保存为: {self.output_file}")
            
        except Exception as e:
            self.logger.error(f"处理失败: {str(e)}")
            raise


def main():
    """主函数"""
    print("=== 累加器日志处理工具 ===")
    print("功能: 将accumulator.log转换为结构化CSV格式")
    
    # 创建处理器并运行
    processor = LogProcessor("accumulator.log")
    processor.run()


if __name__ == "__main__":
    main()
