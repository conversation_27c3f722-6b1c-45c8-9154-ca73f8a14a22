#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键运行累加器日志分析工具

功能：
1. 自动运行日志处理脚本
2. 自动运行数据分析脚本
3. 显示处理结果和生成的文件

作者：AI Assistant
日期：2025-06-19
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{'='*60}")
    print(f"正在执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
            
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
            
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {str(e)}")
        return False


def check_files():
    """检查必要的文件是否存在"""
    required_files = [
        "accumulator.log",
        "log_processor.py", 
        "data_analyzer.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True


def check_dependencies():
    """检查Python依赖包"""
    required_packages = ["pandas", "numpy", "matplotlib", "seaborn"]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少必要的Python包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有必要的Python包都已安装")
    return True


def show_results():
    """显示生成的文件"""
    print(f"\n{'='*60}")
    print("📊 分析完成！生成的文件:")
    print(f"{'='*60}")
    
    # 检查生成的文件
    files_to_check = [
        ("accumulator_data.csv", "结构化数据文件"),
        ("log_processor.log", "日志处理过程记录"),
        ("analysis_output/analysis_report.txt", "文本分析报告"),
        ("analysis_output/analysis_report.json", "详细JSON报告"),
        ("analysis_output/feature_type_distribution.png", "特征类型分布图"),
        ("analysis_output/memory_distribution.png", "内存使用分布图"),
        ("analysis_output/memory_efficiency_by_type.png", "内存效率对比图"),
        ("analysis_output/load_factor_distribution.png", "负载因子分布图"),
        ("data_analyzer.log", "数据分析过程记录")
    ]
    
    existing_files = []
    for file_path, description in files_to_check:
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            existing_files.append((file_path, description, file_size))
            print(f"✅ {description}")
            print(f"   文件: {file_path} ({file_size:,} 字节)")
        else:
            print(f"❌ {description}")
            print(f"   文件: {file_path} (未找到)")
    
    if existing_files:
        print(f"\n📁 总共生成了 {len(existing_files)} 个文件")
        
        # 显示分析报告摘要
        report_file = "analysis_output/analysis_report.txt"
        if Path(report_file).exists():
            print(f"\n📋 分析报告摘要:")
            print("-" * 40)
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content)
            except Exception as e:
                print(f"读取报告文件时出错: {e}")


def main():
    """主函数"""
    print("🚀 累加器日志分析工具")
    print("=" * 60)
    
    # 检查文件和依赖
    if not check_files():
        sys.exit(1)
        
    if not check_dependencies():
        sys.exit(1)
    
    print("\n🔄 开始分析流程...")
    
    # 步骤1: 处理日志文件
    success1 = run_command(
        "python log_processor.py",
        "步骤1: 处理日志文件"
    )
    
    if not success1:
        print("❌ 日志处理失败，停止执行")
        sys.exit(1)
    
    # 步骤2: 数据分析
    success2 = run_command(
        "python data_analyzer.py", 
        "步骤2: 数据分析和可视化"
    )
    
    if not success2:
        print("❌ 数据分析失败")
        sys.exit(1)
    
    # 显示结果
    show_results()
    
    print(f"\n🎉 分析完成！")
    print("您可以查看生成的报告和图表文件。")


if __name__ == "__main__":
    main()
