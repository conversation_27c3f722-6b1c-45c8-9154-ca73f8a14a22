# 累加器日志分析工具

这是一套用于处理和分析百度反作弊特征库累加器日志的Python工具集。

## 功能概述

### 1. 日志处理脚本 (`log_processor.py`)
- 解析 `accumulator.log` 文件
- 提取特征统计信息（ID、类型、元素数、内存使用等）
- 处理不同格式的日志条目
- 清理和标准化数据
- 导出为结构化CSV格式
- 包含完整的错误处理和日志记录

### 2. 数据分析脚本 (`data_analyzer.py`)
- 加载CSV数据进行统计分析
- 生成趋势、模式和异常分析
- 创建可视化图表（分布图、箱线图等）
- 输出详细的分析报告
- 提供优化建议

## 安装依赖

```bash
pip install pandas numpy matplotlib seaborn
```

## 使用方法

### 方法一：一键运行（推荐）

```bash
python run_analysis.py
```

这个脚本会自动：
- 检查必要文件和依赖包
- 运行日志处理脚本
- 运行数据分析脚本
- 显示生成的文件和分析结果

### 方法二：分步运行

#### 第一步：处理日志文件

```bash
python log_processor.py
```

这将：
- 读取 `accumulator.log` 文件
- 解析日志内容并提取结构化数据
- 生成 `accumulator_data.csv` 文件
- 创建处理日志 `log_processor.log`

#### 第二步：分析数据

```bash
python data_analyzer.py
```

这将：
- 读取 `accumulator_data.csv` 文件
- 进行统计分析和异常检测
- 生成可视化图表
- 创建分析报告

## 输出文件

### 日志处理输出
- `accumulator_data.csv` - 结构化的特征数据
- `log_processor.log` - 处理过程日志

### 数据分析输出（在 `analysis_output/` 目录下）
- `analysis_report.json` - 详细的JSON格式分析报告
- `analysis_report.txt` - 文本摘要报告
- `feature_type_distribution.png` - 特征类型分布饼图
- `memory_distribution.png` - 内存使用分布直方图
- `memory_efficiency_by_type.png` - 不同特征类型的内存效率箱线图
- `load_factor_distribution.png` - 负载因子分布直方图
- `data_analyzer.log` - 分析过程日志

## CSV数据结构

生成的CSV文件包含以下主要字段：

### 基础信息
- `timestamp` - 时间戳
- `manager` - 管理器名称
- `feature_id` - 特征ID
- `feature_type` - 特征类型（SEGMENT/RATIO/DISTRIBUTION）
- `line_number` - 原始日志行号

### 元素统计
- `total_elements` - 总元素数
- `segment_elements` - 段元素数
- `window_elements` - 窗口元素数

### 内存信息
- `total_memory` - 总内存使用
- `pool_memory` - 内存池使用
- `overhead_memory` - 开销内存
- `avg_per_element` - 平均每元素内存
- `memory_available` - 内存信息是否可用

### 配置参数
- `step_len` - 步长
- `step_num` - 步数
- `segment_len` - 段长度
- `oldest_coord` - 最旧坐标
- `latest_coord` - 最新坐标

### 哈希表信息
- `segment_size` - 段大小
- `bucket_count` - 桶数量
- `load_factor` - 负载因子
- `empty_buckets` - 空桶数
- `max_chain_len` - 最大链长
- `avg_chain_len` - 平均链长

### 内存池信息
- `node_num` - 节点数
- `malloc_num` - 分配数
- `used_blocks` - 使用块数
- `mem_consume` - 内存消耗
- `actual_need` - 实际需求
- `actual_used` - 实际使用

## 分析功能

### 基础统计
- 数据概览（记录数、特征类型分布等）
- 内存使用统计（均值、中位数、标准差等）
- 元素数量统计
- 性能指标统计

### 异常检测
- **内存异常**：使用IQR方法检测内存使用异常值
- **性能异常**：识别内存效率低下的特征
- **负载因子异常**：检测哈希表负载因子异常

### 可视化分析
- 特征类型分布饼图
- 内存使用分布直方图
- 不同特征类型的内存效率对比
- 负载因子分布分析

### 优化建议
基于分析结果自动生成优化建议，包括：
- 内存优化策略
- 性能调优建议
- 哈希表配置优化

## 错误处理

两个脚本都包含完善的错误处理机制：
- 文件不存在检查
- 数据格式验证
- 异常日志记录
- 优雅的错误恢复

## 日志格式支持

支持解析以下类型的日志条目：
- 监控头信息
- 管理器信息
- 特征基础信息（包含内存详情）
- 特征基础信息（Memory=N/A）
- 特征详细信息（Details行）

## 自定义配置

可以通过修改脚本参数来自定义：
- 输入/输出文件路径
- 异常检测阈值
- 图表样式和大小
- 报告格式

## 注意事项

1. 确保 `accumulator.log` 文件在当前目录下
2. 需要安装所有依赖包
3. 分析脚本需要先运行日志处理脚本
4. 生成的图表需要中文字体支持
5. 大文件处理可能需要较长时间

## 故障排除

### 常见问题
1. **文件编码问题**：脚本使用UTF-8编码，确保日志文件编码正确
2. **内存不足**：大文件可能需要更多内存
3. **图表显示问题**：确保安装了中文字体
4. **依赖包问题**：使用pip安装所有必需的包

### 日志文件
查看生成的日志文件获取详细的错误信息：
- `log_processor.log` - 日志处理错误
- `data_analyzer.log` - 数据分析错误

## 扩展功能

可以根据需要扩展以下功能：
- 实时日志监控
- 更多可视化图表类型
- 自定义异常检测规则
- 数据库存储支持
- Web界面展示

## 联系信息

如有问题或建议，请查看生成的日志文件或联系开发团队。
