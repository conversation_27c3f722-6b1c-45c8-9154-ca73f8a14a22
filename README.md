# 累加器日志分析工具

这是一套用于处理和分析百度反作弊特征库累加器日志的Python工具集。

## 功能概述

### 1. 日志处理脚本 (`log_processor.py`)
- 解析 `accumulator.log` 文件
- 提取特征统计信息（ID、类型、元素数、内存使用等）
- 处理不同格式的日志条目
- 清理和标准化数据
- 导出为结构化CSV格式
- 包含完整的错误处理和日志记录

### 2. 数据分析脚本 (`simple_analyzer.py`)
- 只分析内存占用不是N/A的Feature
- 生成按Feature类型聚合的Excel报告
- 类型内按FeatureID排序
- 创建修复中文显示问题的可视化图表
- 输出简要分析报告

## 安装依赖

```bash
pip install pandas numpy matplotlib seaborn
```

## 使用方法

### 方法一：批量分析（推荐）

```bash
python run_batch.py <日志文件夹路径>
```

或者直接运行（会提示输入文件夹路径）：
```bash
python run_batch.py
```

这个脚本会自动：
- 分析指定文件夹中的所有日志文件（.log 和 .txt）
- 为每个日志文件生成独立的分析结果
- 结果保存到以日志文件名命名的子目录中
- 生成批量分析汇总报告

### 方法二：单文件分析

```bash
python run_simple.py
```

这个脚本会自动：
- 处理当前目录下的accumulator.log文件
- 生成按Feature类型聚合的Excel报告
- 只分析内存占用不是N/A的Feature
- 生成修复中文显示问题的图表

### 方法三：分步运行

#### 第一步：处理日志文件

```bash
python log_processor.py
```

这将：
- 读取 `accumulator.log` 文件
- 解析日志内容并提取结构化数据
- 生成 `accumulator_data.csv` 文件
- 创建处理日志 `log_processor.log`

#### 第二步：分析数据

```bash
python simple_analyzer.py
```

这将：
- 读取 `accumulator_data.csv` 文件
- 生成按Feature类型聚合的Excel报告
- 创建可视化图表
- 生成分析摘要

## 输出文件

### 批量分析输出
```
batch_results/
├── batch_summary.txt                           # 批量分析汇总报告
├── <日志文件名1>/                               # 以日志文件名命名的子目录
│   ├── accumulator.log                         # 原始日志文件副本
│   ├── <日志名>_accumulator_data.csv            # 带前缀的结构化数据
│   ├── <日志名>_feature_detailed_analysis.xlsx  # 带前缀的Excel详细报告
│   ├── <日志名>_analysis_summary.txt            # 带前缀的分析摘要
│   ├── charts/                                 # 图表目录
│   │   ├── <日志名>_feature_type_distribution.png
│   │   ├── <日志名>_memory_usage_by_type.png
│   │   └── <日志名>_memory_efficiency.png
│   └── log_processor.log                       # 处理日志
├── <日志文件名2>/
│   └── ...
└── <日志文件名N>/
    └── ...
```

### 单文件分析输出
- `accumulator_data.csv` - 结构化的特征数据
- `feature_detailed_analysis.xlsx` - **加强版Excel报告**（包含所有详细数据供人工分析）
  - **总览工作表**：各Feature类型的汇总统计
  - **全部数据工作表**：所有Feature的完整数据（按类型聚合，类型内按FeatureID排序）
  - **DISTRIBUTION_详情**：分布类型Feature的所有详细字段
  - **RATIO_详情**：比率类型Feature的所有详细字段
  - **SEGMENT_详情**：段类型Feature的所有详细字段
  - 包含36个数据列：FeatureID、内存使用、元素数量、Segment统计、内存池信息、坐标信息等
- `analysis_summary.txt` - 简要分析报告
- `charts/` - 图表目录（修复中文显示问题）
  - `feature_type_distribution.png` - Feature类型分布
  - `memory_usage_by_type.png` - 各类型内存使用对比
  - `memory_efficiency.png` - 内存效率分析
- `log_processor.log` - 处理过程日志

## CSV数据结构

生成的CSV文件包含以下主要字段：

### 基础信息
- `timestamp` - 时间戳
- `manager` - 管理器名称
- `feature_id` - 特征ID
- `feature_type` - 特征类型（SEGMENT/RATIO/DISTRIBUTION）
- `line_number` - 原始日志行号

### 元素统计
- `total_elements` - 总元素数
- `segment_elements` - 段元素数
- `window_elements` - 窗口元素数

### 内存信息
- `total_memory` - 总内存使用
- `pool_memory` - 内存池使用
- `overhead_memory` - 开销内存
- `avg_per_element` - 平均每元素内存
- `memory_available` - 内存信息是否可用

### 配置参数
- `step_len` - 步长
- `step_num` - 步数
- `segment_len` - 段长度
- `oldest_coord` - 最旧坐标
- `latest_coord` - 最新坐标

### 哈希表信息
- `segment_size` - 段大小
- `bucket_count` - 桶数量
- `load_factor` - 负载因子
- `empty_buckets` - 空桶数
- `max_chain_len` - 最大链长
- `avg_chain_len` - 平均链长

### 内存池信息
- `node_num` - 节点数
- `malloc_num` - 分配数
- `used_blocks` - 使用块数
- `mem_consume` - 内存消耗
- `actual_need` - 实际需求
- `actual_used` - 实际使用

## 分析功能

### 基础统计
- 按Feature类型聚合统计
- 内存使用统计（总量、平均值等）
- 元素数量统计
- 内存效率分析

### Excel详细报告
- **总览工作表**：各Feature类型汇总统计
- **全部数据工作表**：所有Feature完整数据，按类型聚合，类型内按FeatureID排序
- **分类详情工作表**：按Feature类型分别展示所有详细字段
- 包含36个数据列（按重要性排序）：
  - **基础信息**：Feature类型、FeatureID、管理器
  - **元素统计**：总元素数、段元素数、窗口元素数
  - **内存信息**：总内存、内存池、开销内存、平均每元素内存
  - **Segment统计**：Segment[Size/BucketCount/LoadFactor]
  - **HashMap统计**：HashMap[Size/Buckets/LoadFactor/EmptyBuckets/NonEmptyBuckets/MaxChainLen/AvgChainLen]
  - **内存池信息**：MemPool[NodeNum/MallocNum/UsedBlocks/MemConsume/ActualNeed/ActualUsed]
  - **窗口步骤**：WindowSteps详情（步骤大小组合字符串）
  - **坐标信息**：Coords[StepLen/StepNum/MaxStepNum/SegmentLen/OldestCoord/SegmentStartCoord/LatestCoord]
  - **计算列**：内存(MB)、内存效率(字节/元素)
- 只包含有效内存数据（非N/A），便于人工分析

### 可视化分析
- Feature类型分布图
- 各类型内存使用对比
- 内存效率散点图
- 修复中文字体显示问题

## 错误处理

两个脚本都包含完善的错误处理机制：
- 文件不存在检查
- 数据格式验证
- 异常日志记录
- 优雅的错误恢复

## 日志格式支持

支持解析以下类型的日志条目：
- 监控头信息
- 管理器信息
- 特征基础信息（包含内存详情）
- 特征基础信息（Memory=N/A）
- 特征详细信息（Details行）

## 注意事项

1. 确保 `accumulator.log` 文件在当前目录下
2. 需要安装依赖包：`pip install pandas numpy matplotlib openpyxl`
3. 分析脚本需要先运行日志处理脚本
4. 脚本会自动处理中文字体显示问题
5. 只分析内存占用不是N/A的Feature

## 故障排除

### 常见问题
1. **文件编码问题**：脚本使用UTF-8编码，确保日志文件编码正确
2. **依赖包问题**：运行 `pip install pandas numpy matplotlib openpyxl`
3. **图表显示问题**：脚本会自动检测和设置中文字体

### 日志文件
查看生成的日志文件获取详细的错误信息：
- `log_processor.log` - 日志处理错误
